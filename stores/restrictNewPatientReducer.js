import { createSlice } from '@reduxjs/toolkit';

const InitialState = {
  fetchingAPI: false,
  allowed: true, // Default to true to allow appointments until API response
  error: null,
};

const RestrictNewPatientSlice = createSlice({
  name: 'RestrictNewPatientSlice',
  initialState: InitialState,
  reducers: {
    setRestrictNewPatientFetching: (state, action) => {
      state.fetchingAPI = action.payload;
    },
    setRestrictNewPatientAllowed: (state, action) => {
      state.allowed = action.payload;
      state.error = null;
    },
    setRestrictNewPatientError: (state, action) => {
      state.error = action.payload;
      state.fetchingAPI = false;
    },
    resetRestrictNewPatientState: (state) => {
      state.fetchingAPI = InitialState.fetchingAPI;
      state.allowed = InitialState.allowed;
      state.error = InitialState.error;
    },
  },
  extraReducers: (builder) => builder.addCase('RESET_STATE', () => InitialState),
});

export const { 
  setRestrictNewPatientFetching, 
  setRestrictNewPatientAllowed, 
  setRestrictNewPatientError,
  resetRestrictNewPatientState 
} = RestrictNewPatientSlice.actions;

export default RestrictNewPatientSlice.reducer;
