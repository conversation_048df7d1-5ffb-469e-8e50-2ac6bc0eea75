import {configureStore} from '@reduxjs/toolkit';
import { Platform } from 'react-native';
import userReducer from './userReducer';
import appointmentListReducer from './appointmentListReducer';
import rsisenmappingReducer from './rsisenmappingReducer';
import medicationListReducer from './medicationListReducer';
import notificationsListReducer from './notificationsListReducer';
import physiciansListReducer from './physiciansListReducer';
import locationListReducer from './locationListReducer';
import purposeListReducer from './purposeListReducer';
import tokenReducer from './tokenReducer';
import personalInfoTransReducer from './personalInfoTransReducer';
import pharmacyListReducer from './pharmacyListReducer';
import pathReducer from './tabpressReducer';
import reminderReducer from './reminderReducer';
import userPracticeReducer from './userPracticeReducer';
import insuranceimage from './insuranceimage';
import restrictNewPatientReducer from './restrictNewPatientReducer';

const store = configureStore({
  reducer: {
    userInfo: userReducer,
    UserToken: tokenReducer,
    AppointmentDetails: appointmentListReducer,
    RsiSenMappingDetails: rsisenmappingReducer,
    MedicationDetails: medicationListReducer,
    NotificationList: notificationsListReducer,
    PharmacyList: pharmacyListReducer,
    PhysicianList: physiciansListReducer,
    LocationList: locationListReducer,
    PurposeList: purposeListReducer,
    PersonalInfoTransaction: personalInfoTransReducer,
    Path: pathReducer,
    Reminder: reminderReducer,
    reduxPractice: userPracticeReducer,
    insuranceimage: insuranceimage,
    RestrictNewPatient: restrictNewPatientReducer,
  }
});

export default store;
