import HandleApiCallErr from '../helpers/handleApiCallError';
import {
  setRestrictNewPatientFetching,
  setRestrictNewPatientAllowed,
  setRestrictNewPatientError
} from '../stores/restrictNewPatientReducer';
import { axiosPrivate } from '../api/axiosPrivate';

const { default: config } = require('../config');

const GetRestrictNewPatient = (dispatch, setErrorModal, setResponseErrorMsg, practice) => {
  dispatch(setRestrictNewPatientFetching(true));

  const url = `${config[config.STAGING].PATIENT_INFO}restrict-new-patient`;


  axiosPrivate
    .post(url, { practice })
    .then(response => {
      if (response.data.status === 200) {
      
        const allowed = response.data.allow_booking;
        dispatch(setRestrictNewPatientAllowed(allowed));
      } else {
        setResponseErrorMsg(response.data.message || 'Error fetching restrict new patient data');
        setErrorModal(true);
        dispatch(setRestrictNewPatientError(response.data.message || 'Error fetching restrict new patient data'));
      }
      dispatch(setRestrictNewPatientFetching(false));
    })
    .catch(err => {
      console.log('Error fetching restrict new patient data:', err);
      const errorMessage = HandleApiCallErr(err).message;
      setResponseErrorMsg(errorMessage);
      setErrorModal(true);
      dispatch(setRestrictNewPatientError(errorMessage));
      dispatch(setRestrictNewPatientFetching(false));
    });
};

export default GetRestrictNewPatient;
