buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.22"
        googlePlayServicesVersion = "+" // default: "+"
        firebaseMessagingVersion = "21.1.0" 
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath "com.google.gms:google-services:4.4.2"
        classpath "com.google.firebase:firebase-crashlytics-gradle:3.0.2"
    }
}

apply plugin: "com.facebook.react.rootproject"
