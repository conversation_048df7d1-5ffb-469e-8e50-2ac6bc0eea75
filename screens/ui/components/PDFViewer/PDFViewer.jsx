import React, {memo, useState} from 'react';
import {
  StyleSheet,
  Dimensions,
  View,
  SafeAreaView,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import Pdf from 'react-native-pdf';
import {useThemeContext} from '../../theme';
import {theme} from '../../theme/default/theme';
import CustomText from '../customText/customText';
import ReactNativeBlobUtil from 'react-native-blob-util';
import DownloadIcon from './assets/DownloadIcon';

const PDFViewer = ({fileName, fileUrl, showPdf, setShowPdf}) => {
  const {s} = useThemeContext(createStyle);
  const source = {uri: fileUrl, cache: true};
  const [isDownloading, setIsDownloading] = useState(false);
  //const source = require('./test.pdf');  // ios only
  //const source = {uri:'bundle-assets://test.pdf' };
  //const source = {uri:'file:///sdcard/test.pdf'};
  //const source = {uri:"data:application/pdf;base64,JVBERi0xLjcKJc..."};
  //const source = {uri:"content://com.example.blobs/xxxxxxxx-...?offset=0&size=xxx"};
  //const source = {uri:"blob:xxxxxxxx-...?offset=0&size=xxx"};
  // const [showModal, setShowModal] = useState(false);

  // Check current storage permission status
  const checkStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const androidVersion = Platform.Version;

        // For Android 13+ (API 33+), no special permission needed for Downloads folder
        if (androidVersion >= 33) {
          // Android 13+ uses scoped storage, no permission needed for Downloads folder
          return true;
        } else if (androidVersion >= 29) {
          // Android 10-12 - scoped storage, no permission needed for Downloads folder
          return true;
        } else {
          // Android 9 and below - check WRITE_EXTERNAL_STORAGE
          const hasPermission = await PermissionsAndroid.check(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          );
          return hasPermission;
        }
      } catch (err) {
        console.warn('Permission check error:', err);
        return false;
      }
    }
    return true; // iOS doesn't need explicit permission for downloads
  };

  // Request storage permission for Android
  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const androidVersion = Platform.Version;

        // First check if we already have permission
        const hasPermission = await checkStoragePermission();
        if (hasPermission) {
          return true;
        }

        // For Android 13+ (API 33+), we don't need special permissions for Downloads folder
        if (androidVersion >= 33) {
          return true;
        } else if (androidVersion >= 29) {
          // Android 10-12 uses scoped storage, no permission needed for Downloads folder
          return true;
        } else {
          // For Android 9 and below, request the traditional permission
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            {
              title: 'Storage Permission Required',
              message:
                'This app needs access to storage to download files to your device.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'Allow',
            },
          );
          return granted === PermissionsAndroid.RESULTS.GRANTED;
        }
      } catch (err) {
        console.warn('Permission request error:', err);
        return false;
      }
    }
    return true; // iOS doesn't need explicit permission for downloads
  };

  // Fallback method: Open file in native browser for download
  const openInBrowser = async () => {
    try {
      const supported = await Linking.canOpenURL(fileUrl);
      if (supported) {
        await Linking.openURL(fileUrl);
        Alert.alert(
          'File Opened in Browser',
          'The file has been opened in your browser. You can download it from there.',
          [{text: 'OK'}],
        );
      } else {
        Alert.alert(
          'Cannot Open File',
          'Unable to open the file. Please check if you have a browser installed.',
          [{text: 'OK'}],
        );
      }
    } catch (error) {
      console.error('Error opening file in browser:', error);
      Alert.alert('Error', 'Failed to open the file in browser.', [
        {text: 'OK'},
      ]);
    }
  };

  // Download PDF function
  const downloadPDF = async () => {
    if (!fileUrl || !fileName) {
      Alert.alert('Error', 'No file available to download');
      return;
    }

    setIsDownloading(true);

    try {
      // Check and request storage permission first
      const hasPermission = await requestStoragePermission();
      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'Storage permission is required to download files. You can grant permission in settings or open the file in your browser instead.',
          [
            {text: 'Cancel', style: 'cancel'},
            {
              text: 'Open Settings',
              onPress: () => {
                // On Android, this will open app settings where user can grant permissions
                if (Platform.OS === 'android') {
                  ReactNativeBlobUtil.android.actionViewIntent(
                    'android.settings.APPLICATION_DETAILS_SETTINGS',
                    'package:' + ReactNativeBlobUtil.android.packageName,
                  );
                }
              },
            },
            {text: 'Open in Browser', onPress: () => openInBrowser()},
          ],
        );
        setIsDownloading(false);
        return;
      }

      if (Platform.OS === 'ios') {
        // iOS: Download to Documents directory
        const downloadDest = `${ReactNativeBlobUtil.fs.dirs.DocumentDir}/${fileName}`;

        const downloadResult = await ReactNativeBlobUtil.config({
          fileCache: true,
          path: downloadDest,
        }).fetch('GET', fileUrl);

        if (downloadResult.info().status === 200) {
          Alert.alert('Download Complete', 'File saved to Documents folder', [
            {text: 'OK'},
          ]);
        } else {
          Alert.alert('Download Failed', 'Failed to download the file');
        }
      } else {
        // Android: Use MediaStore API for proper Downloads folder visibility
        const downloadConfig = {
          fileCache: true,
          addAndroidDownloads: {
            useDownloadManager: true,
            notification: true,
            title: fileName,
            description: 'PDF file downloaded',
            mime: 'application/pdf',
            mediaScannable: true,
          },
        };

        console.log('Starting download with config:', downloadConfig);
        const downloadResult = await ReactNativeBlobUtil.config(
          downloadConfig,
        ).fetch('GET', fileUrl);

        console.log('Download result info:', downloadResult.info());
        console.log('Download result path:', downloadResult.path());

        // Check download success with multiple validation methods
        const downloadPath = downloadResult.path();
        const downloadInfo = downloadResult.info();
        const status = downloadInfo.status;

        // Validate download success
        let downloadSuccess = false;

        if (downloadPath && downloadPath.length > 0) {
          // Primary check: valid download path
          downloadSuccess = true;
        } else if (status === 200 || status === '200') {
          // Fallback: check status code
          downloadSuccess = true;
        }

        if (downloadSuccess) {
          Alert.alert(
            'Download Complete',
            `${fileName} has been saved to your Downloads folder`,
            [{text: 'OK'}],
          );
        } else {
          console.error(
            'Download failed - Status:',
            status,
            'Path:',
            downloadPath,
          );
          Alert.alert(
            'Download Failed',
            `Failed to download the file. Would you like to try opening it in your browser instead?`,
            [
              {text: 'Cancel', style: 'cancel'},
              {text: 'Retry Download', onPress: () => downloadPDF()},
              {text: 'Open in Browser', onPress: () => openInBrowser()},
            ],
          );
        }
      }
    } catch (error) {
      console.error('Download error:', error);

      // Provide more specific error messages based on error type
      let errorMessage = 'An error occurred while downloading the file.';

      if (error.message && error.message.includes('Network')) {
        errorMessage =
          'Network error. Please check your internet connection and try again.';
      } else if (error.message && error.message.includes('permission')) {
        errorMessage =
          'Permission error. Please check app permissions in device settings.';
      } else if (error.message && error.message.includes('space')) {
        errorMessage =
          'Insufficient storage space. Please free up some space and try again.';
      }

      Alert.alert('Download Failed', errorMessage, [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Retry Download', onPress: () => downloadPDF()},
        {text: 'Open in Browser', onPress: () => openInBrowser()},
      ]);
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <Modal
        animationType="fade"
        transparent={false}
        visible={showPdf}
        onRequestClose={() => {
          setShowPdf(false);
        }}>
        <View style={s?.container}>
          <View style={s?.titleContainer}>
            <View style={s?.titleTextContainer}>
              <CustomText style={s?.titleText} numberOfLines={1}>
                {fileName}
              </CustomText>
            </View>
            <View style={s?.buttonContainer}>
              <TouchableOpacity
                style={s?.downloadBtn}
                onPress={downloadPDF}
                disabled={isDownloading}>
                {isDownloading ? (
                  <ActivityIndicator
                    size="small"
                    color={theme.colors.brandPrimary}
                  />
                ) : (
                  <DownloadIcon size={20} color={theme.colors.brandPrimary} />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={s?.crossBtn}
                onPress={() => setShowPdf(false)}>
                <CustomText
                  style={{
                    fontSize: 22,
                    fontWeight: '600',
                    color: 'black',
                    textAlign: 'right',
                  }}>
                  x
                </CustomText>
              </TouchableOpacity>
            </View>
          </View>
          <Pdf
            // renderActivityIndicator={<ActivityIndicator />}
            source={source}
            trustAllCerts={false}
            // onLoadComplete={(numberOfPages, filePath) => {
            //     console.log(`Number of pages: ${numberOfPages}`);
            // }}
            renderActivityIndicator={() => (
              <ActivityIndicator color={theme.colors.brandPrimary} />
            )}
            // onPageChanged={(page, numberOfPages) => {
            //     console.log(`Current page: ${page}`);
            // }}
            onError={error => {
              // setShowPdf(false);
              console.log('pdf error: ', error);
            }}
            onPressLink={uri => {
              console.log(`Link pressed: ${uri}`);
            }}
            style={s?.pdf}
          />
        </View>
      </Modal>
    </SafeAreaView>
  );
};

PDFViewer.defaultProps = {
  fileName: undefined,
  fileUrl: undefined,
};

const createStyle = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'flex-start',
      alignItems: 'center',
      marginTop: 45,
    },
    titleContainer: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 10,
    },
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 15,
    },
    downloadBtn: {
      padding: 8,
      borderRadius: 5,
      backgroundColor: 'rgba(0, 123, 255, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: 36,
      minHeight: 36,
    },
    pdf: {
      flex: 1,
      width: Dimensions.get('window').width,
      height: Dimensions.get('window').height,
    },
    titleTextContainer: {
      flexShrink: 1,
      justifyContent: 'space-between',
      alignItems: 'center',
      flex: 1,
      flexDirection: 'row',
    },
    titleText: {
      fontSize: 16,
      color: 'black',
      fontWeight: '600',
      width: '90%',
      // display: 'flex',
    },
    text: {
      ...theme.fonts.texts.h4,
      color: theme.colors.darkGrey,
      paddingVertical: theme.metrics.x4,
    },
    crossBtn: {
      position: 'relative',
      textAlign: 'right',
      padding: 5,
    },
  });

const PDFViewerComponent = memo(PDFViewer);
export {PDFViewerComponent as PDFViewer};
