import messaging from '@react-native-firebase/messaging';
import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {useEffect, useState} from 'react';
import {Image, Linking, StyleSheet, View, Platform, Alert} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useDispatch, useSelector} from 'react-redux';
import {setClickListner} from '../../stores/personalInfoTransReducer';
import {setIsModal} from '../../stores/userReducer';
import PlainLoader from '../Plainloader/PlainLoader';
import AppointmentDetails from '../appointmentScreen/appointmentDetailsScreen/appointmentDetailsScreen';
import AppointmentResultDetails from '../appointmentScreen/appointmentDetailsScreen/appointmentResultDetailsScreen';
import AppointmentScreen from '../appointmentScreen/appointmentScreen';
import CancelAppointment from '../appointmentScreen/cancelAppointment/cancelAppointment';
import ChangeAppointment from '../appointmentScreen/changeAppointmentScreen/changeAppointmentScreen';
import NewAppointment from '../appointmentScreen/newAppointment/newAppointment';
import ForgotPasswordScreen from '../authScreen/forgotPasswordScreen/forgotPasswordScreen';
import ForgotPwdOtpScreen from '../authScreen/forgotPasswordScreen/otpScreen/otpScreen';
import LoginScreen from '../authScreen/loginScreen/loginScreen';
import OtpScreen from '../authScreen/otpScreen/otpScreen';
import NewPassword from '../authScreen/signUpScreen/NewPassword';
import TermsAndConditions from '../authScreen/signUpScreen/TermsAndConditions';
import VerifyLoader from '../authScreen/signUpScreen/VerifyLoader';
import SignUpScreen from '../authScreen/signUpScreen/signUpScreen';
import ChatListScreen from '../chatListScreen/chatListScreen';
import ChatScreen from '../chatScreen/chatScreen';
import HomeScreen from '../homeScreen/homeScreen';
import MedicineScreen from '../medicinesScreen/medicineScreen';
import OnBoardingScreen from '../onBoardingScreen/onBoardingScreens';
import AddReminderScreen from '../profileScreen/addReminderScreen/addReminderScreen';
import EnterUserInfoScreen from '../profileScreen/addUsersScreen/enterUserInformationScreen/enterUserInformationScreen';
import ChangePwdScreen from '../profileScreen/changePasswordScreen/changePasswordScreen';
import MyProfileScreen from '../profileScreen/myProfileScreen/myProfileScreen';
import PaymentHistoryScreen from '../profileScreen/paymentHistoryScreen/paymentHistoryScreen';
import PersonalInfoScreen from '../profileScreen/personalInformationScreen/personalInfoScreen';
import ReminderScreen from '../profileScreen/reminderScreen/reminderScreen';
import CustomDrawerContent from './CustomDrawerContent';
import {setChangePasswordModal} from '../../stores/userReducer';
import {setReduxPractice} from '../../stores/userPracticeReducer';
import InsuranceListScreen from '../profileScreen/InsuranceScreen/InsuranceListScreen';
import AddInsuranceScreen from '../profileScreen/InsuranceScreen/AddInsuranceScreen';
import InsuranceCameraScreen from '../profileScreen/InsuranceScreen/InsuranceCameraScreen';
import EditInsuranceData from '../profileScreen/InsuranceScreen/EditInsuranceScreen';
import CheckMobileNumberScreen from '../authScreen/signUpScreen/CheckMobileNumberScreen';
import OnBoardingVideoScreen from '../onBoardingScreen/OnBoardingVideoScreen';
import LoginOtpScreen from '../authScreen/loginScreen/LoginOtpScreen';
import LoginErrorScreen from '../authScreen/loginScreen/LoginErrorScreen';

const HomeImg = require('../../assets/images/png/Home.png');
const MyProfileImg = require('../../assets/images/png/My-profile.png');
const MedicinesImg = require('../../assets/images/png/Medicines.png');
const MyAppointmentsImg = require('../../assets/images/png/My-appointments.png');
const HomeImgSelected = require('../../assets/images/png/Home-selected.png');
const MyProfileImgSelected = require('../../assets/images/png/My-profile-selected.png');
const MedicinesImgSelected = require('../../assets/images/png/Medicines-selected.png');
const MyAppointmentsImgSelected = require('../../assets/images/png/My-appointments-selected.png');
const MyChatImg = require('../../assets/images/png/chat.png');
const MyChatImgSelected = require('../../assets/images/png/chat-selected.png');

const AppointmentStack = createNativeStackNavigator();
const MyProfileStack = createNativeStackNavigator();
const HomeStack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();
const MainStack = createNativeStackNavigator();
const Drawer = createDrawerNavigator();

function AppointmentStackScreen({navigation}) {
  // useEffect(() => {
  //   const unsubscribe = navigation.addListener('tabPress', e => {
  //     // Prevent default behavior
  //     e.preventDefault();

  //     // Reset navigation state of HomeStack
  //     navigation.reset({
  //       index: 0,
  //       routes: [{name: 'Appointments'}],
  //     });
  //   });

  //   return unsubscribe;
  // }, [navigation]);
  return (
    <AppointmentStack.Navigator>
      <AppointmentStack.Screen
        name="Appointments"
        component={AppointmentScreen}
        initialParams={{selectView: 1}}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="AppointmentsResult"
        component={AppointmentScreen}
        initialParams={{selectView: 2}}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="ChangeAppointment"
        component={ChangeAppointment}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="CancelAppointment"
        component={CancelAppointment}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="AppointmentDetails"
        component={AppointmentDetails}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="AppointmentResultDetails"
        component={AppointmentResultDetails}
        options={{headerShown: false}}
      />
      <AppointmentStack.Screen
        name="NewAppointment"
        component={NewAppointment}
        options={{headerShown: false}}
      />
      {/* <AppointmentStack.Screen
        name="ChatListScreen"
        component={ChatListScreen}
        options={{ headerShown: false }}
      /> */}
      <AppointmentStack.Screen
        name="ChatScreen"
        component={ChatScreen}
        options={{headerShown: false}}
      />
    </AppointmentStack.Navigator>
  );
}

function HomeStackScreen({navigation, route}) {
  // useEffect(() => {
  //   const unsubscribe = navigation.addListener('tabPress', e => {
  //     // Prevent default behavior
  //     e.preventDefault();

  //     // Reset navigation state of HomeStack
  //     navigation.reset({
  //       index: 0,
  //       routes: [{name: 'HomeMain'}],
  //     });
  //   });

  //   return unsubscribe;
  // }, [navigation]);

  return (
    <HomeStack.Navigator>
      <HomeStack.Screen
        name="DrawerHome"
        component={DrawerStack}
        options={{headerShown: false}}
      />

      <HomeStack.Screen
        name="Appointments"
        component={AppointmentScreen}
        initialParams={{selectView: 1}}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="AppointmentsResult"
        component={AppointmentScreen}
        initialParams={{selectView: 2}}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="Terms and Conditions 1"
        component={TermsAndConditions}
        initialParams={{fromScreen: 'Home'}}
        options={{
          headerShown: false,
          title: 'Terms and Conditions',
          navigationBarHidden: true,
        }}
      />
      <HomeStack.Screen
        name="AppointmentDetails"
        component={AppointmentDetails}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="AppointmentResultDetails"
        component={AppointmentResultDetails}
        options={{headerShown: false}}
      />
      {/* <HomeStack.Screen
        name="ChatListScreen"
        component={ChatListScreen}
        options={{ headerShown: false }}
      /> */}
      <HomeStack.Screen
        name="ChatScreen"
        component={ChatScreen}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="ChangeAppointment"
        component={ChangeAppointment}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="CancelAppointment"
        component={CancelAppointment}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="NewAppointment"
        component={NewAppointment}
        options={{headerShown: false}}
      />
      <HomeStack.Screen
        name="Plainloader"
        component={PlainLoader}
        options={{headerShown: false}}
      />
      {/* <HomeStack.Screen
        name="logout"
        component={HandleLogout}
        options={{headerShown: false}}
      /> */}
    </HomeStack.Navigator>
  );
}
function MyProfileStackScreen({navigation}) {
  const {pendingTransactions} = useSelector(
    state => state.PersonalInfoTransaction,
  );
  useEffect(() => {
    const unsubscribe = navigation.addListener('tabPress', e => {
      // Prevent default behavior
      e.preventDefault();

      // Reset navigation state of HomeStack
      if (!pendingTransactions) {
        navigation.reset({
          index: 0,
          routes: [{name: 'Profile'}],
        });
      }
    });

    return unsubscribe;
  }, [navigation]);
  return (
    <MyProfileStack.Navigator>
      <MyProfileStack.Screen
        name="Profile"
        component={MyProfileScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="PersonalInformationScreen"
        component={PersonalInfoScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="RemindersScreen"
        component={ReminderScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="InsuranceListScreen"
        component={InsuranceListScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="AddInsuranceScreen"
        component={AddInsuranceScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="InsuranceCameraScreen"
        component={InsuranceCameraScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="PaymentHistoryScreen"
        component={PaymentHistoryScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="ChangePwdScreen"
        component={ChangePwdScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="EnterUserInfoScreen"
        component={EnterUserInfoScreen}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="EditInsuranceDataScreen"
        component={EditInsuranceData}
        options={{headerShown: false}}
      />
      <MyProfileStack.Screen
        name="AddReminderScreen"
        component={AddReminderScreen}
        options={{headerShown: false}}
      />
    </MyProfileStack.Navigator>
  );
}

function DrawerStack({navigation, route}) {
  return (
    <Drawer.Navigator
      screenOptions={{
        drawerPosition: 'right',
        drawerStyle: {
          width: '100%',
        },
        swipeEnabled: false,
      }}
      backBehavior="history"
      drawerContent={props => <CustomDrawerContent {...props} />}>
      {/* <Drawer.Screen name="Feed" component={AppointmentScreen} /> */}
      <Drawer.Screen
        options={{headerShown: false}}
        name="Home"
        component={HomeScreen}
      />
    </Drawer.Navigator>
  );
}

const InnerStackNavigator = () => {
  const {pendingTransactions} = useSelector(
    state => state.PersonalInfoTransaction,
  );
  const change = useSelector(state => state.userInfo.changed);
  const dispatch = useDispatch();
  const [showTabBar, setShowTabBar] = useState(true);

  const reminderHome = useSelector(state => state.userInfo.reminderHome);
  const logoutLoader = useSelector(state => state.userInfo.logout_loader);
  const isPasswordChanged = useSelector(state => state.userInfo.changePassword);
  const fetchingUserAPI = useSelector(state => state.userInfo.fetchingUserAPI);
  const fetchingAPI = useSelector(
    state => state.AppointmentDetails.fetchingAPI,
  );

  // alert(reminderHome)
  // useEffect(() => {
  //   enableBackButtonHandler();

  //   return () => {
  //     disableBackButtonHandler();
  //   };
  // }, []);

  // const backHandler = () => {
  //   BackHandler.exitApp();
  //   return true;
  // };
  // const tab = useSelector((state) => state.Tab.tabpress)
  // const handleTabBarHide = () => {

  //   setShowTabBar(!showTabBar);
  // }
  const preferred_practice = useSelector(
    state => state.reduxPractice.reduxPractice,
  );
  return (
    <Tab.Navigator
      // initialRouteName='Home'
      screenOptions={({route}) => ({
        labelStyle: {
          allowFontScaling: false,
        },
        tabBarIcon: ({focused, color, size}) => {
          let iconName;
          console.log('route called :');
          if (route.name === 'Home') {
            iconName = focused
              ? 'ios-information-circle'
              : 'ios-information-circle-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'ios-list' : 'ios-list-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        headerShown: false,
        tabBarActiveTintColor: '#0079BC',
        tabBarInactiveTintColor: 'gray',
        tabBarHideOnKeyboard: true,
      })}
      screenListeners={({navigation, route}) => ({
        tabPress: e => {
          e.preventDefault();
          const state = navigation.getState();
          // console.log('checkingRoute: ', state.routeNames[state.index], " | ", route.name, " | ", state.routeNames[state.index] === route.name)
          // if(state.routeNames[state.index] === route.name) return true
          if (
            state.routeNames[state.index] === 'Profile' &&
            pendingTransactions
          ) {
            dispatch(
              setClickListner({
                status: true,
                path: route.name,
              }),
            );
          } else if (
            state.routeNames[state.index] === 'Profile' &&
            isPasswordChanged
          ) {
            dispatch(setChangePasswordModal(true));
          } else if (
            state.routeNames[state.index] === 'Home' ||
            'Profile' ||
            'Chat' ||
            'Appointments' ||
            'Medicines'
          ) {
            const params = {
              from: 'bottomStack',
            };
            change
              ? dispatch(setIsModal(true))
              : navigation.reset({
                  index: 0,
                  routes: [{name: route.name, params}],
                });
            dispatch(setReduxPractice(preferred_practice));
            // isPasswordChanged ? dispatch(setChangePasswordModal(true)) : navigation.reset({
            //   index: 0,
            //   routes: [{ name: route.name, params}],
            // });
          } else if (route.name === 'Chat') {
            const params = {
              ...route.params,
              fromScreen: 'Tab',
            };
            navigation.reset({
              index: 0,
              routes: [{name: route.name, params}],
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [{name: route.name}],
            });
          }
        },
      })}
      detachInactiveScreens={true}>
      <Tab.Screen
        name="Home"
        component={HomeStackScreen}
        // listeners={({navigation}) => ({
        //   tabPress: (e) => {
        //     alert('changes have not saved')
        //   }
        // })}
        options={{
          // tabBarStyle:{display:change ? 'none' : null},
          tabBarStyle: {
            display:
              reminderHome && !logoutLoader && !fetchingUserAPI && !fetchingAPI
                ? null
                : 'none',
          },
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image source={focused ? HomeImgSelected : HomeImg} />
          ),
        }}
      />
      <Tab.Screen
        name="Appointments"
        component={AppointmentStackScreen}
        initialParams={{selectView: 1}}
        options={{
          allowFontScaling: false,
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image
              source={focused ? MyAppointmentsImgSelected : MyAppointmentsImg}
            />
          ),
        }}
      />
      <Tab.Screen
        name="Medicines"
        component={MedicineScreen}
        initialParams={{selectView: 1}}
        options={{
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image source={focused ? MedicinesImgSelected : MedicinesImg} />
          ),
        }}
      />
      <Tab.Screen
        name="Chat"
        component={ChatListScreen}
        initialParams={{fromScreen: 'Tab'}}
        // listeners={({navigation}) => ({
        //   tabPress: (e) => {
        //     navigation.navigate('AppointmentScreen')
        //   }
        // })}
        options={{
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image source={focused ? MyChatImgSelected : MyChatImg} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={MyProfileStackScreen}
        options={{
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image source={focused ? MyProfileImgSelected : MyProfileImg} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

const NavigationTabs = ({}) => {
  const userToken = useSelector(state => state.UserToken);

  const PublicLinkingConfig = {
    prefixes: [
      'clinicweb-dev.clearhealthcare.ai://',
      'https://clinicweb-dev.clearhealthcare.ai',
      'clinicweb.clearhealthcare.ai://',
      'https://clinicweb.clearhealthcare.ai',
      'clearhealthcare://',
    ],
    config: {
      screens: {
        LoginScreen: {
          path: 'token/:token',
          parse: {
            token: token => decodeURIComponent(token),
            fromScreen: () => 'deepLink',
          },
        },
      },
    },
    async getInitialURL() {
      // Check if app was opened from a deep link
      const url = await Linking.getInitialURL();
      if (url != null) {
        return url;
      }
      if (url === 'token') {
        alert('token');
      }

      // Check if there is an initial firebase notification
      const message = await messaging().getInitialNotification();
      // Get the `url` property from the notification which corresponds to a screen
      // This property needs to be set on the notification payload when sending it
      return message?.data?.link;
    },
    subscribe(listener) {
      const onReceiveURL = ({url}) => {
        listener(url);
      };

      // Listen to incoming links from deep linking
      const subscription = Linking.addEventListener('url', onReceiveURL);
      // const message = messaging().getInitialNotification();
      // console.log('MESSAGE', message);
      const onMessage = messaging().onNotificationOpenedApp(message => {
        const url = message?.data?.link;
        if (url) {
          listener(url);
        }
      });

      // Listen to firebase push notifications
      // const unsubscribeNotification = messaging().onNotificationOpenedApp(
      //   (message) => {
      //     console.log('MESSAGE12',message);
      //     const url = message?.data?.link;

      //     if (url) {
      //       // Any custom logic to check whether the URL needs to be handled

      //       // Call the listener to let React Navigation handle the URL
      //       listener(url);
      //     }
      //   },
      // );

      return () => {
        // Clean up the event listeners
        subscription.remove();
        onMessage();
      };
    },
  };

  const ProtectedLinkingConfig = {
    prefixes: [
      'clinicweb-dev.clearhealthcare.ai://',
      'https://clinicweb-dev.clearhealthcare.ai',
      'clinicweb.clearhealthcare.ai://',
      'https://clinicweb.clearhealthcare.ai',
      'clearhealthcare://',
    ],
    config: {
      screens: {
        HomeStack: {
          screens: {
            Chat: {
              path: 'chat/:practice/:chartId/:chattype',
            },
          },
        },
      },
    },
    async getInitialURL() {
      // Check if app was opened from a deep link
      const url = await Linking.getInitialURL();
      console.log('URLLL', url);
      if (url != null) {
        return url;
      }
      if (url === 'token') {
        alert('token');
      }

      // Check if there is an initial firebase notification
      const message = await messaging().getInitialNotification();
      // Get the `url` property from the notification which corresponds to a screen
      // This property needs to be set on the notification payload when sending it
      return message?.data?.link;
    },
    subscribe(listener) {
      const onReceiveURL = ({url}) => {
        listener(url);
      };

      // Listen to incoming links from deep linking
      const subscription = Linking.addEventListener('url', onReceiveURL);
      // const message = messaging().getInitialNotification();
      const onMessage = messaging().onNotificationOpenedApp(message => {
        const url = message?.data?.link;
        if (url) {
          listener(url);
        }
      });

      // Listen to firebase push notifications
      // const unsubscribeNotification = messaging().onNotificationOpenedApp(
      //   (message) => {
      //     console.log('MESSAGE12',message);
      //     const url = message?.data?.link;

      //     if (url) {
      //       // Any custom logic to check whether the URL needs to be handled

      //       // Call the listener to let React Navigation handle the URL
      //       listener(url);
      //     }
      //   },
      // );

      return () => {
        // Clean up the event listeners
        subscription.remove();
        onMessage();
        // Linking.removeEventListener('url', onReceiveURL);
        // unsubscribeNotification();
      };
    },
  };

  // if(fetching) return (

  // )

  const [isAppUpdateAvailable, setAppUpdateAvailable] = React.useState(false);

  const openAppStore = () => {
    const appStoreUrlIOS =
      'itms-apps://itunes.apple.com/app/ClearHealthcare/id6450694559?l=id';
    const playStoreUrlAndroid =
      'https://play.google.com/store/apps/details?id=com.coherent.clearhealthcare';

    const url = Platform.select({
      ios: appStoreUrlIOS,
      android: playStoreUrlAndroid,
    });
    console.log('opening url', url);
    Linking.openURL(url);
  };

  function compareVersions(v1, v2) {
    const v1Parts = v1.split('.').map(Number);
    const v2Parts = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) {
        return 1;
      } else if (v1Part < v2Part) {
        return -1;
      }
    }

    return 0;
  }

  React.useEffect(() => {
    async function getVersion() {
      console.log('useeffect trig');
      await axiosPublic
        .get(
          `${config[config.STAGING].COGNITO_URL}version-control?os=${Platform.OS}`,
        )
        .then(res => {
          let comparison = compareVersions(res.data, packageJson.version);
          if (comparison > 0) {
            console.log('x > y');
            setAppUpdateAvailable(true);
          } else {
            console.log('x is equal to y');
            setAppUpdateAvailable(false);
          }
          if (res?.data) {
          }
        })
        .catch(e => 'error occured for:', e);
    }
    getVersion();
  }, []);

  return (
    <>
      {/* {userToken.isFetching
      ? (
        <>
          <View>
            <Text>Fetching</Text>
          </View>
        </>
      ) : ( */}
      {/* <NavigationContainer linking={userToken.AccessToken ? LinkingConfig: null}></NavigationContainer> */}
      <NavigationContainer
        linking={
          userToken.AccessToken ? ProtectedLinkingConfig : PublicLinkingConfig
        }>
        <MainStack.Navigator
          initialRouteName={
            userToken.AccessToken ? 'HomeStack' : 'OnBoardingScreen'
          }
          screenOptions={{headerTitleAlign: 'center'}}>
          {/* {console.log('checkkk: ', userToken.AccessToken)} */}
          {!userToken.AccessToken ? (
            // <MainStack.Group>
            <>
              <MainStack.Screen
                name="OnBoardingVideoScreen"
                component={OnBoardingVideoScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="OnBoardingScreen"
                component={OnBoardingScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="LoginErrorScreen"
                component={LoginErrorScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="LoginScreen"
                component={LoginScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />

              <MainStack.Screen
                name="LoginOtpScreen"
                component={LoginOtpScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />

              <MainStack.Screen
                name="CheckUserByMobileNumber"
                component={CheckMobileNumberScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />

              <MainStack.Screen
                name="SignupScreen"
                component={SignUpScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="Terms and Conditions"
                component={TermsAndConditions}
                options={{headerShown: true, headerBackVisible: false}}
                initialParams={{headerShown: false, fromScreen: 'LoginScreen'}}
              />
              <MainStack.Screen
                name="VerifyLoader"
                component={VerifyLoader}
                initialParams={{fromScreen: 'deepLink'}}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="NewPasswordScreen"
                component={NewPassword}
                initialParams={{fromScreen: 'deepLink'}}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="OtpScreen"
                component={OtpScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              <MainStack.Screen
                name="ForgotPasswordScreen"
                component={ForgotPasswordScreen}
                options={{
                  headerShown: true,
                  tabBarVisible: false,
                  headerTitle: '',
                  headerTransparent: true,
                  headerBackTitleVisible: false,
                }}
              />
              <MainStack.Screen
                name="ForgotPwdOtpScreen"
                component={ForgotPwdOtpScreen}
                options={{headerShown: false, tabBarVisible: false}}
              />
              {/* <MainStack.Screen
            name="Home"
            component={InnerStackNavigator}
            options={{ headerShown: false }}
          /> */}
            </>
          ) : (
            // </MainStack.Group>
            // <MainStack.Group>
            <MainStack.Screen
              name="HomeStack"
              component={InnerStackNavigator}
              options={{headerShown: false}}
            />
            // </MainStack.Group>
          )}
        </MainStack.Navigator>
        
      </NavigationContainer>
      {/* )} */}
    </>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 200,
    height: 200,
  },
  updatePopupContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  updateTextContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    margin: 30,
    padding: 12,
    width: '80%',
    gap: 20,
  },
  infoText: {
    fontSize: 20,
  },
});

export default NavigationTabs;
