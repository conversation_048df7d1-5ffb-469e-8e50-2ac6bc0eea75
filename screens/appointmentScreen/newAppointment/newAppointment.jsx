import {useEffect, useState} from 'react';
import moment from 'moment';
import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Keyboard,
  View,
  Image,
  Linking,
} from 'react-native';
import {ConvertDateToStr, formatTime} from '../../../helpers/convertDateToStr';
import {Button} from '../../ui/components/Button';
import {Header} from '../../ui/components/Header';
import {OurTextInput} from '../../ui/components/OurTextInput';
import {Picker} from '../../ui/components/Picker';
import {useThemeContext} from '../../ui/theme';
import {theme} from '../../ui/theme/default/theme';
import CreateActions from '../../../hooks/createActions';
import {
  ErrorMsgHandler,
  SlotMsgHandler,
  SuccessMsgHandler,
} from '../../ui/components/MessageHandler/messageHandler';
import {useSelector} from 'react-redux';
import DataPicker from '../../ui/components/DataPicker/DataPicker';
import GetPracticeInfo from '../../../helpers/getPracticeInfo';
import config from '../../../config';
import {axiosPrivate} from '../../../api/axiosPrivate';
import {generateTimeSlots} from '../../../utils/generateTimeSlots';
const rsiImg = require('../../../assets/images/png/rsi-img.png');
const senImg = require('../../../assets/images/png/sen-img.png');
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {faL} from '@fortawesome/free-solid-svg-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {log} from 'react-native-reanimated';
import {ApptFormatDate} from '../../../utils/dateHandler';
import {setAsyncStorage} from '../../../helpers/AppointmentRestrictionHelper';
import {DefaultModal} from '../../ui/components/Modals/DefaultModal';
import {CancelCal} from '../assets/cancelCal';
const NewAppointment = ({navigation}) => {
  const {s} = useThemeContext(createStyle);
  const [details, setDetails] = useState({
    appointment_date: '',
    appointment_time: '',
    doctor_name: '',
    diagnosis: '',
    appointment_location: '',
    appointment_purpose: '',
    note: '',
    locationId: '',
    appointmentid: '',
  });

  const [btnLoader, setBtnLoader] = useState(false);
  // const [dateModal, setDateModal] = useState(false);
  // const [date, setDate] = useState(new Date());
  // const [textDate, setTextDate] = useState('');
  const [timeModal, setTimeModal] = useState(false);
  const [time, setTime] = useState(new Date());
  const [textTime, setTextTime] = useState('');
  const [requestModal, setRequestModal] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setresponseErrorMsg] = useState(false);
  const [preferredPrac, setPreferredPrac] = useState('');
  const [phyList, setPhyList] = useState([]);
  const [diagnosisList, setDiagnosisList] = useState([]);

  const [autoPopulateBlockType, setAutoPopulateBlockType] = useState([]);

  const [locList, setLocList] = useState([]);
  const [appPopup, setAppPopup] = useState(false);
  const [noSlotsMessage, setNoSlotsMessage] = useState('');

  const [purList, setPurList] = useState([]);
  const [availabilityDate, setAvailabilityDate] = useState([]);

  const [availableTimeSlot, setavailableTimeSlot] = useState([]);
  const [physicianDetails, setPhysicianDetails] = useState({
    isFetching: false,
    data: [],
  });
  const userInfo = useSelector(state => state.userInfo.userInfo);
  const allSlice = useSelector(state => state);
  // Start with the physician dropdown disabled until appointment type is selected
  const [phy_name, set_PhyName] = useState(true);
  const [select_location, set_SelectLocation] = useState(true);
  const [apt_date, set_AptDate] = useState(true);
  const [apt_slot, set_AptSlot] = useState(true);
  const [openSlots, setOpenSlots] = useState([]);
  const [slotres, setSlotres] = useState([]);

  const [required_physician, set_RequiredPhysicians] = useState();
  const [loader, setLoader] = useState(false);
  const [apptDate, setApptDate] = useState('');

  const [physicianId, setPhysicianId] = useState('');
  const [locationId, setLocationId] = useState('');
  const [appointmentType, setAppointmentType] = useState('');
  const [keyboardOpened, setKeyboardOpened] = useState(false);
  const [apptTypeLoader, setapptTypeLoader] = useState(false);
  const [physicianLoader, setPhysicianLoader] = useState(false);
  const [dateLoader, setDateLoader] = useState(false);
  const [timeSlotLoader, setTimeSlotLoader] = useState(false);
  const [changingPhysician, setChangingPhysician] = useState(false);
  const [hasAutoPopulatedPhysician, setHasAutoPopulatedPhysician] =
    useState(false);
  const [isAutoPopulationInProgress, setIsAutoPopulationInProgress] =
    useState(false);
  const PhysiciansList = useSelector(state => state.PhysicianList.data);
  const [providerInfo, setProviderInfo] = useState([]);

  // const LocationList = useSelector(state => state.LocationList.data);
  const PurposeList = useSelector(state => state.PurposeList.PurposeDetails);
  const [lastAppointmentData, setLastAppointmentData] = useState({
    ui_dropdown: null,
    location: null,
    location_id: null,
  });
  const upcomingAppointments = useSelector(
    state => state.AppointmentDetails.upcomingAppointments,
  );
  const rsi_segAPI = useSelector(
    state => state.RsiSenMappingDetails.rsiSenMappingData,
  );

  const dob = useSelector(state => state.userInfo?.userInfo?.dob);
  const previousAppointments = useSelector(
    state => state.AppointmentDetails.previousAppointments,
  );

  const handleDoneRequest = async () => {
    // This is always the Call for Requesting button - make a phone call
    try {
      const practice = await GetPracticeInfo();
      let number = '';
      if (Platform.OS === 'ios') {
        if (practice.preferred_practice === 'rsi') {
          number = 'telprompt:+18504766759';
        } else if (practice.preferred_practice === 'sen') {
          number = 'telprompt:+12519903937';
        }
      } else {
        if (practice.preferred_practice === 'rsi') {
          number = 'tel:8504766759';
        } else if (practice.preferred_practice === 'sen') {
          number = 'tel:2519903937';
        }
      }
      Linking.openURL(number);
    } catch (e) {}
  };

  useEffect(() => {
    // Check if we have auto-populate block types and preferred practice loaded
    if (!autoPopulateBlockType || !preferredPrac) return;

    // Determine which appointment list to use (upcoming or previous)
    let appointmentList = [];
    let isUsingPreviousAppointment = false;

    // If we have upcoming appointments, use them
    if (upcomingAppointments.length > 0) {
      // For upcoming appointments, sort by date in descending order (newest first)
      appointmentList = [...upcomingAppointments].sort((a, b) => {
        // Convert date strings to Date objects for comparison
        // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
        let dateA, dateB;

        if (a.appt_date) {
          // Format: YYYY-MM-DD
          dateA = new Date(a.appt_date);
        } else if (a.date) {
          // Format: MM/DD/YYYY
          const parts = a.date.split('/');
          dateA = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (a.appointmentdate) {
          dateA = new Date(a.appointmentdate);
        }

        if (b.appt_date) {
          // Format: YYYY-MM-DD
          dateB = new Date(b.appt_date);
        } else if (b.date) {
          // Format: MM/DD/YYYY
          const parts = b.date.split('/');
          dateB = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (b.appointmentdate) {
          dateB = new Date(b.appointmentdate);
        }

        return dateB - dateA; // Descending order (newest first)
      });
    }
    // If no upcoming appointments, use previous appointments
    else if (previousAppointments && previousAppointments.length > 0) {
      // For previous appointments, sort by date in descending order (newest first)
      appointmentList = [...previousAppointments].sort((a, b) => {
        // Convert date strings to Date objects for comparison
        // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
        let dateA, dateB;

        if (a.appt_date) {
          // Format: YYYY-MM-DD
          dateA = new Date(a.appt_date);
        } else if (a.date) {
          // Format: MM/DD/YYYY
          const parts = a.date.split('/');
          dateA = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (a.appointmentdate) {
          dateA = new Date(a.appointmentdate);
        }

        if (b.appt_date) {
          // Format: YYYY-MM-DD
          dateB = new Date(b.appt_date);
        } else if (b.date) {
          // Format: MM/DD/YYYY
          const parts = b.date.split('/');
          dateB = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (b.appointmentdate) {
          dateB = new Date(b.appointmentdate);
        }

        return dateB - dateA; // Descending order (newest first)
      });
      isUsingPreviousAppointment = true;
    } else {
      return;
    }

    // Get the most recent appointment (first after sorting by date descending)
    const lastAppointment = appointmentList[0];

    // Log detailed information about the selected appointment

    // IMPORTANT: Only proceed with auto-population if ui_dropdown is not null/undefined
    if (!lastAppointment.ui_dropdown) {
      return;
    }

    // Check if any item in autoPopulateBlockType is present in ui_dropdown
    const shouldBlockAutoPopulate =
      autoPopulateBlockType &&
      lastAppointment.ui_dropdown &&
      autoPopulateBlockType.some(blockType =>
        lastAppointment.ui_dropdown.includes(blockType),
      );

    // Store last appointment data for reference
    setLastAppointmentData({
      ui_dropdown: lastAppointment.ui_dropdown,
      location: lastAppointment.location,
      location_id: lastAppointment.source_location_id,
      shouldBlockAutoPopulate: shouldBlockAutoPopulate,
    });

    // Start auto-population process - disable location field
    setIsAutoPopulationInProgress(true);

    // Safety timeout to ensure auto-population state is cleared
    const locationAutoPopulateTimeout = setTimeout(() => {
      console.log(
        'Location auto-populate safety timeout reached, clearing state',
      );
      setIsAutoPopulationInProgress(false);
    }, 5000); // 5 second safety timeout

    console.log('Auto-populating location with appointment data:', {
      departmentid: lastAppointment.departmentid,
      location_name: lastAppointment.location_name,
      source_location_id: lastAppointment.source_location_id,
      location: lastAppointment.location,
    });

    // Auto-populate location first (even if auto-population is blocked for other fields)
    // Check for both possible location field combinations
    if (
      (lastAppointment.departmentid && lastAppointment.location_name) ||
      (lastAppointment.source_location_id && lastAppointment.location)
    ) {
      const locationIdToUse =
        lastAppointment.departmentid || lastAppointment.source_location_id;
      const locationNameToUse =
        lastAppointment.location_name || lastAppointment.location;

      console.log('Setting location:', {locationIdToUse, locationNameToUse});

      setLocationId(locationIdToUse);
      setDetails(prevDetails => ({
        ...prevDetails,
        appointment_location: locationNameToUse,
        locationId: locationIdToUse,
      }));
      set_SelectLocation(false);

      // Clear the auto-population progress after location is set
      setTimeout(() => {
        setIsAutoPopulationInProgress(false);
        clearTimeout(locationAutoPopulateTimeout);
        console.log('Location auto-population completed');
      }, 1000);
    } else {
      // If no location data is available, clear the auto-population progress immediately
      console.log('No location data available for auto-population');
      setIsAutoPopulationInProgress(false);
      clearTimeout(locationAutoPopulateTimeout);
    }
  }, [
    upcomingAppointments,
    previousAppointments,
    autoPopulateBlockType,
    preferredPrac,
  ]);

  // This effect runs when locationId changes to auto-populate appointment type
  useEffect(() => {
    // Skip if locationId is empty or preferredPrac is not set
    if (!locationId || !preferredPrac || locationId === '') return;

    // Determine which appointment list to use (upcoming or previous)
    let appointmentList = [];
    let isUsingPreviousAppointment = false;

    // If we have upcoming appointments, use them
    if (upcomingAppointments.length > 0) {
      // For upcoming appointments, sort by date in descending order (newest first)
      appointmentList = [...upcomingAppointments].sort((a, b) => {
        // Convert date strings to Date objects for comparison
        // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
        let dateA, dateB;

        if (a.appt_date) {
          // Format: YYYY-MM-DD
          dateA = new Date(a.appt_date);
        } else if (a.date) {
          // Format: MM/DD/YYYY
          const parts = a.date.split('/');
          dateA = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (a.appointmentdate) {
          dateA = new Date(a.appointmentdate);
        }

        if (b.appt_date) {
          // Format: YYYY-MM-DD
          dateB = new Date(b.appt_date);
        } else if (b.date) {
          // Format: MM/DD/YYYY
          const parts = b.date.split('/');
          dateB = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (b.appointmentdate) {
          dateB = new Date(b.appointmentdate);
        }

        return dateB - dateA; // Descending order (newest first)
      });
    }
    // If no upcoming appointments, use previous appointments
    else if (previousAppointments && previousAppointments.length > 0) {
      console.log('previousAppointments', previousAppointments);
      // For previous appointments, sort by date in descending order (newest first)
      appointmentList = [...previousAppointments].sort((a, b) => {
        // Convert date strings to Date objects for comparison
        // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
        let dateA, dateB;

        if (a.appt_date) {
          // Format: YYYY-MM-DD
          dateA = new Date(a.appt_date);
        } else if (a.date) {
          // Format: MM/DD/YYYY
          const parts = a.date.split('/');
          dateA = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (a.appointmentdate) {
          dateA = new Date(a.appointmentdate);
        }

        if (b.appt_date) {
          // Format: YYYY-MM-DD
          dateB = new Date(b.appt_date);
        } else if (b.date) {
          // Format: MM/DD/YYYY
          const parts = b.date.split('/');
          dateB = new Date(parts[2], parts[0] - 1, parts[1]);
        } else if (b.appointmentdate) {
          dateB = new Date(b.appointmentdate);
        }

        return dateB - dateA; // Descending order (newest first)
      });
      isUsingPreviousAppointment = true;
    } else {
      return;
    }

    // Get the most recent appointment (first after sorting by date descending)
    const lastAppointment = appointmentList[0];

    console.log('Auto-population debug - Last appointment:', {
      ui_dropdown: lastAppointment.ui_dropdown,
      source_location_id: lastAppointment.source_location_id,
      departmentid: lastAppointment.departmentid,
      physician_fname: lastAppointment.physician_fname,
      physician_lname: lastAppointment.physician_lname,
      location: lastAppointment.location,
      currentLocationId: locationId,
    });

    // IMPORTANT: Only proceed with auto-population if ui_dropdown is not null/undefined
    if (!lastAppointment.ui_dropdown) {
      console.log('Skipping auto-population: ui_dropdown is null/undefined');
      return;
    }

    // For now, let's be more lenient with location matching to allow auto-population to proceed
    // We can make this stricter later if needed
    const isFromAutoPopulation =
      !locationId || // If no location is selected yet, allow auto-population
      lastAppointment.departmentid === locationId ||
      lastAppointment.source_location_id === locationId ||
      lastAppointment.source_location_id === parseInt(locationId) ||
      lastAppointment.departmentid === parseInt(locationId);

    console.log('Auto-population check:', {
      lastAppointmentLocationId:
        lastAppointment.source_location_id || lastAppointment.departmentid,
      currentLocationId: locationId,
      isFromAutoPopulation,
      locationIdType: typeof locationId,
      sourceLocationIdType: typeof lastAppointment.source_location_id,
    });

    if (!isFromAutoPopulation) {
      console.log('Skipping auto-population: location mismatch');
      return;
    }

    // Check if any item in autoPopulateBlockType is present in ui_dropdown
    // If so, we should not auto-populate
    const shouldBlockAutoPopulate =
      autoPopulateBlockType &&
      lastAppointment.ui_dropdown &&
      autoPopulateBlockType.some(blockType =>
        lastAppointment.ui_dropdown.includes(blockType),
      );

    console.log('Auto-population block check:', {
      shouldBlockAutoPopulate,
      autoPopulateBlockType,
      ui_dropdown: lastAppointment.ui_dropdown,
    });

    if (shouldBlockAutoPopulate) {
      console.log('Skipping auto-population: blocked by autoPopulateBlockType');
      return;
    }

    console.log('Starting auto-population process...');

    (async () => {
      // Set up a timeout to prevent infinite loading
      const autoPopulateTimeout = setTimeout(() => {
        console.log('Auto-populate timeout reached, clearing loaders');
        setapptTypeLoader(false);
        setPhysicianLoader(false);
        setIsAutoPopulationInProgress(false);
      }, 15000); // 15 second timeout

      try {
        console.log('Step 1: Getting patient type...');
        // Get patient type
        let patientType = await getneworeshtablished();
        console.log('Patient type received:', patientType);

        console.log('Step 2: Getting appointment types...');
        // Get appointment types for this location
        let appointmentTypes = await getApptTypeApiCall(patientType);
        console.log('Appointment types received:', appointmentTypes);

        if (!appointmentTypes || !Array.isArray(appointmentTypes)) {
          clearTimeout(autoPopulateTimeout);
          return;
        }

        // Format appointment types for dropdown
        const convertedApptTypeData = appointmentTypes.map(element => ({
          value: element.ui_dropdown,
          label: element.ui_dropdown,
        }));
        setPurList(convertedApptTypeData);

        // We already have the lastAppointment from above, no need to get it again

        // Determine the appointment purpose to use
        let appointmentPurpose = 'Established';
        if (lastAppointment.ui_dropdown) {
          appointmentPurpose = lastAppointment.ui_dropdown;
        } else if (
          lastAppointment.appointmenttype &&
          lastAppointment.appointmenttype.includes('op')
        ) {
          appointmentPurpose = 'Established';
        }

        console.log('Step 3: Checking appointment type validity...');
        console.log('Appointment purpose to match:', appointmentPurpose);
        console.log('Available appointment types:', convertedApptTypeData);

        // Check if this appointment type exists in the available types
        const validAppointmentType = convertedApptTypeData.find(
          type =>
            type.value === appointmentPurpose ||
            type.value.includes(appointmentPurpose),
        );

        console.log('Valid appointment type found:', validAppointmentType);

        // Check if any item in autoPopulateBlockType is present in ui_dropdown
        // If so, we should not auto-populate the appointment type
        const shouldBlockAppointmentTypeAutoPopulate =
          autoPopulateBlockType &&
          lastAppointment.ui_dropdown &&
          autoPopulateBlockType.some(blockType =>
            lastAppointment.ui_dropdown.includes(blockType),
          );

        console.log(
          'Should block appointment type auto-populate:',
          shouldBlockAppointmentTypeAutoPopulate,
        );

        if (shouldBlockAppointmentTypeAutoPopulate) {
          console.log('Blocking appointment type auto-population');
          clearTimeout(autoPopulateTimeout);
        } else if (validAppointmentType) {
          console.log(
            'Step 4: Setting appointment type and getting physicians...',
          );
          // Set the appointment type
          setDetails(prevDetails => ({
            ...prevDetails,
            appointment_purpose: validAppointmentType.value,
          }));
          setAppointmentType(validAppointmentType.value);

          console.log(
            'Calling openSlotApiCall with:',
            validAppointmentType.value,
          );
          // Now get physicians for this appointment type
          const res = await openSlotApiCall(
            validAppointmentType.value,
            'autopop',
          );

          console.log('openSlotApiCall response:', res);

          if (res && res.response) {
            console.log('Step 5: Processing physician data...');
            // Find the physician from the last appointment
            const apptData = organizeAppointments(res.response);
            setOpenSlots(apptData);

            // Determine which physician to auto-select
            let physicianToAutoSelect = null;

            if (res.providername) {
              physicianToAutoSelect = res.providername;
            } else if (
              lastAppointment.physician_fname &&
              lastAppointment.physician_lname
            ) {
              // Try to match by physician name
              const fullName = `${lastAppointment.physician_fname} ${lastAppointment.physician_lname}`;

              physicianToAutoSelect = fullName;
            }

            console.log('Physician to auto-select:', physicianToAutoSelect);
            console.log('Appointment data:', apptData);

            console.log('Step 6: Calling HandlePhyName...');
            // Pass the physician to auto-select to HandlePhyName
            await HandlePhyName(apptData, physicianToAutoSelect);

            // Always make sure the physician dropdown is enabled
            set_PhyName(false);

            // If we have a physician name, set up available dates
            if (details.doctor_name) {
              HandleApptDates(details.doctor_name, apptData);
            } else if (physicianToAutoSelect) {
              // Find the physician in the available list
              const physicianNames = Object.keys(apptData);
              const matchedPhysician = physicianNames.find(name =>
                name
                  .toLowerCase()
                  .includes(physicianToAutoSelect.toLowerCase()),
              );

              if (matchedPhysician) {
                HandleApptDates(matchedPhysician, apptData);
              }
            }
          }
          console.log('Step 7: Auto-population completed successfully');
          clearTimeout(autoPopulateTimeout);
        } else {
          console.log(
            'No valid appointment type found, ending auto-population',
          );
          clearTimeout(autoPopulateTimeout);
        }
      } catch (err) {
        console.error('Error during auto-population:', err);
        clearTimeout(autoPopulateTimeout);
      } finally {
        console.log('Auto-population process finished, clearing loaders');
        // Always clear loaders in finally block
        setapptTypeLoader(false);
        setPhysicianLoader(false);
        setIsAutoPopulationInProgress(false);
      }
    })();
  }, [locationId, preferredPrac, upcomingAppointments, previousAppointments]);

  // showLoader parameter controls whether to show the loading indicator
  const getAllPhysicians = async (
    showLoader = false,
    specificAppointmentType = null,
  ) => {
    try {
      if (showLoader) {
        setPhysicianLoader(true);
      }

      // Try multiple endpoints to get physicians
      let physicians = [];
      let pratice = await GetPracticeInfo();

      // First try the booking-slots endpoint with the selected appointment type
      try {
        // Use the provided specific appointment type first, then the one in details
        // No fallback - we only want physicians for the exact appointment type
        const appointmentTypeToUse =
          specificAppointmentType || details.appointment_purpose;

        // If no appointment type is specified, log a warning and return empty array
        if (!appointmentTypeToUse) {
          setPhysicianLoader(false);
          return [];
        }

        // Make sure we have a valid patient type
        if (!pat_type) {
          // Set a default value if patient type is empty
          setpat_type('Established patient');
        }

        // Use the current patient type - no fallback
        const patientTypeToUse = pat_type || '';

        const slotsRes = await axiosPrivate.get(
          `${config[config.STAGING].PATIENT_INFO}${
            pratice.preferred_practice
          }/booking-slots?location_id=${locationId}&patient_type=${patientTypeToUse}&appt_type_id=${appointmentTypeToUse}`,
        );

        if (
          slotsRes.data &&
          Array.isArray(slotsRes.data) &&
          slotsRes.data.length > 0
        ) {
          // Extract physicians from the slots data
          const slotsPhysicians = slotsRes.data.map(item => ({
            label: `${item.provider.displayname}`,
            value: `${item.provider.displayname}`,
            id: item.provider.providerid,
          }));

          if (slotsPhysicians.length > 0) {
            physicians = slotsPhysicians;
          }
        }
      } catch (error) {}

      // We're not using fallback endpoints anymore to ensure we only get physicians
      // specific to the selected appointment type
      if (physicians.length === 0) {
      }

      // If we have physicians, return them
      if (physicians.length > 0) {
        setPhysicianLoader(false);
        return physicians;
      }

      // If we still don't have physicians, try one more fallback

      // No physicians found, return empty list

      setPhysicianLoader(false);
      return [];
    } catch (e) {
      setPhysicianLoader(false);

      // Return empty list on error

      return [];
    }
  };

  // api call to get all the location
  const getLocationApiCall = async () => {
    setLoader(true);
    let pratice = await GetPracticeInfo();
    return axiosPrivate
      .get(
        `${config[config.STAGING].PATIENT_INFO}${
          pratice.preferred_practice
        }/booking-locations`,
      )
      .then(res => {
        setLoader(false);
        return Promise.resolve(res.data);
      })
      .catch(err => {
        setLoader(false);
        setErrorModal(true);
        setresponseErrorMsg('An error occured while fetching Location');

        return Promise.reject(err);
      });
  };
  const [pat_type, setpat_type] = useState('Established patient');

  const getneworeshtablished = async () => {
    setapptTypeLoader(true);
    let practiceInfo = await GetPracticeInfo();

    try {
      const res = await axiosPrivate.post(
        `${config[config.STAGING].PATIENT_INFO}get-new-or-established`,
        {
          practice: practiceInfo.preferred_practice,
        },
      );

      setapptTypeLoader(false);

      // Make sure we have a valid patient type
      if (res.data && res.data.patient_type) {
        setpat_type(res.data.patient_type);
        return res.data.patient_type;
      } else {
        const defaultPatientType = 'Established patient';
        setpat_type(defaultPatientType);
        return defaultPatientType;
      }
    } catch (error) {
      setapptTypeLoader(false);

      // Use a default value if there's an error
      const defaultPatientType = 'Established patient';
      setpat_type(defaultPatientType);
      return defaultPatientType;
    }
  };
  // start point of the app
  useEffect(() => {
    (async () => {
      // const res = await getRequiredPhysicians()
      const data = await getLocationApiCall();

      HandleLocName(data.location_name);
      setAppPopup(false);
      setLoader(false);
      // set_RequiredPhysicians(res);
    })();
  }, []);

  // use to display the name
  const HandlePhyName = async (
    allData,
    autoSelectPhysician = null,
    autoSelectPhysicianId = null,
  ) => {
    console.log('HandlePhyName called with:', {
      allData,
      autoSelectPhysician,
      autoSelectPhysicianId,
    });

    // Set up a timeout to prevent infinite loading in HandlePhyName
    const handlePhyNameTimeout = setTimeout(() => {
      console.log('HandlePhyName timeout reached, clearing loaders');
      setPhysicianLoader(false);
      setIsAutoPopulationInProgress(false);
      set_PhyName(false); // Enable dropdown for manual selection
    }, 10000); // 10 second timeout

    try {
      let appt = [];

      // Show loader while matching physician
      setPhysicianLoader(true);

      // Force a UI update to ensure the loader is shown
      setTimeout(() => {
        setPhysicianLoader(true);
      }, 0);

      // Check if this is a manual physician change (not initial auto-population)
      const isManualChange =
        (details.doctor_name !== '' && !autoSelectPhysician) ||
        hasAutoPopulatedPhysician;

      // Determine if this is an auto-populate trigger
      const isAutoPopulateTrigger =
        autoSelectPhysician || autoSelectPhysicianId;

      // If this is an auto-population attempt, mark that we've tried it
      if (isAutoPopulateTrigger) {
        setHasAutoPopulatedPhysician(true);
      }

      // IMPORTANT: Don't show popup by default - we'll decide later based on whether we find a match
      // This ensures the popup doesn't show when a matching physician is found
      setAppPopup(false);

      // For manual changes, always enable the dropdown immediately
      if (isManualChange) {
        set_PhyName(false);
        set_AptDate(false);
        set_AptSlot(false);
      } else {
        // For auto-population, keep the dropdown disabled until we find a match

        set_PhyName(true);
      }

      // Make sure the physician loader is shown
      setPhysicianLoader(true);

      // Force a UI update to ensure the loader is shown
      setTimeout(() => {
        setPhysicianLoader(true);
      }, 0);

      // Extract physicians from appointment data

      for (let i in allData) {
        appt.push({label: i, value: i});
      }

      if (appt.length === 0) {
        // Check if we already have physicians loaded
        if (phyList && phyList.length > 0) {
          // Only show popup if we don't have a physician to auto-select AND this is an auto-populate trigger AND not a manual change
          if (
            !autoSelectPhysician &&
            !autoSelectPhysicianId &&
            isAutoPopulateTrigger &&
            !isManualChange
          ) {
            setAppPopup(true);
          } else {
            setAppPopup(false);
          }

          // Only auto-select if explicitly requested and a match is found
          if (autoSelectPhysician) {
            // Look for an exact match first
            let matchedPhysician = phyList.find(
              p => p.value.toLowerCase() === autoSelectPhysician.toLowerCase(),
            );

            // If no exact match, try a partial match
            if (!matchedPhysician) {
              matchedPhysician = phyList.find(
                p =>
                  p.value
                    .toLowerCase()
                    .includes(autoSelectPhysician.toLowerCase()) ||
                  autoSelectPhysician
                    .toLowerCase()
                    .includes(p.value.toLowerCase()),
              );
            }

            if (matchedPhysician) {
              setDetails(prevDetails => ({
                ...prevDetails,
                doctor_name: matchedPhysician.value,
              }));
              set_PhyName(false); // Enable the dropdown so users can change the physician later

              // Don't show the popup since we've auto-selected a physician
              setAppPopup(false);

              // Hide the physician loader
              setPhysicianLoader(false);
              clearTimeout(handlePhyNameTimeout);
            } else {
              // Enable the dropdown for manual selection when no match is found
              set_PhyName(false);

              // Only show popup for initial auto-population, not for manual changes
              if (!isManualChange) {
                setAppPopup(true);
              }
              clearTimeout(handlePhyNameTimeout);
            }
          } else {
            // Always enable the dropdown for manual selection
            set_PhyName(false);
            clearTimeout(handlePhyNameTimeout);
          }

          return;
        }

        // If we don't have pre-loaded physicians, fetch them now

        // Only show popup if we don't have a physician to auto-select AND this is an auto-populate trigger AND not a manual change
        if (
          !autoSelectPhysician &&
          !autoSelectPhysicianId &&
          isAutoPopulateTrigger &&
          !isManualChange
        ) {
          setAppPopup(true);
        } else {
          setAppPopup(false);
        }

        // Fetch all physicians for this location
        try {
          // Pass true to show the loading indicator and the current appointment type
          const currentAppointmentType = details.appointment_purpose;

          // If no appointment type is specified, log a warning and return empty array
          if (!currentAppointmentType) {
            setPhysicianLoader(false);
            setPhyList([]);
            clearTimeout(handlePhyNameTimeout);
            return;
          }

          const allPhysicians = await getAllPhysicians(
            true,
            currentAppointmentType,
          );
          if (allPhysicians && allPhysicians.length > 0) {
            appt = allPhysicians;

            // Store the physician list for later use
            setPhyList(allPhysicians);

            // Only auto-select if explicitly requested and a match is found
            if (autoSelectPhysician) {
              // Look for an exact match first
              let matchedPhysician = allPhysicians.find(
                p =>
                  p.value.toLowerCase() === autoSelectPhysician.toLowerCase(),
              );

              // If no exact match, try a partial match
              if (!matchedPhysician) {
                matchedPhysician = allPhysicians.find(
                  p =>
                    p.value
                      .toLowerCase()
                      .includes(autoSelectPhysician.toLowerCase()) ||
                    autoSelectPhysician
                      .toLowerCase()
                      .includes(p.value.toLowerCase()),
                );
              }

              if (matchedPhysician) {
                setDetails(prevDetails => ({
                  ...prevDetails,
                  doctor_name: matchedPhysician.value,
                }));

                // Now that auto-population is complete, enable the dropdown so users can change the physician later if needed
                setTimeout(() => {
                  set_PhyName(false);
                  clearTimeout(handlePhyNameTimeout);
                }, 500);

                // Don't show the popup since we've auto-selected a physician
                setAppPopup(false);
              } else {
                // Enable the dropdown for manual selection when no match is found
                set_PhyName(false);

                // Only show popup for initial auto-population, not for manual changes
                if (!isManualChange) {
                  setAppPopup(true);
                }
                clearTimeout(handlePhyNameTimeout);
              }
            } else {
              // Always enable the dropdown for manual selection
              set_PhyName(false);
              clearTimeout(handlePhyNameTimeout);
            }

            return;
          } else {
            // No physicians found, use empty list

            appt = [];
          }
        } catch (error) {
          // No physicians found, use empty list
          console.error('Error fetching physicians:', error);
          appt = [];
          setPhysicianLoader(false);
          setIsAutoPopulationInProgress(false);
          clearTimeout(handlePhyNameTimeout);
        }
      } else {
        // Only auto-select if explicitly requested and a match is found
        if (autoSelectPhysician || autoSelectPhysicianId) {
          // Log all available physicians for debugging

          let matchedPhysician = null;

          // First, try to match by ID if available (most reliable method)
          if (autoSelectPhysicianId) {
            matchedPhysician = appt.find(
              p => p.id && p.id.toString() === autoSelectPhysicianId.toString(),
            );

            if (matchedPhysician) {
            } else {
            }
          }

          // If no match by ID, try to match by name
          if (!matchedPhysician && autoSelectPhysician) {
            // Look for an exact match first
            matchedPhysician = appt.find(
              p =>
                p.value.toLowerCase().trim() ===
                autoSelectPhysician.toLowerCase().trim(),
            );

            // If no exact match, try a partial match
            if (!matchedPhysician) {
              // Try different variations of the name
              const nameVariations = [
                autoSelectPhysician,
                autoSelectPhysician.replace(',', ''),
                autoSelectPhysician.replace('Dr.', '').trim(),
                autoSelectPhysician.replace('MD', '').trim(),
                autoSelectPhysician.replace('MD,', '').trim(),
                autoSelectPhysician.replace(', MD', '').trim(),
              ];

              // Try each variation
              for (const variation of nameVariations) {
                for (const physician of appt) {
                  const physicianName = physician.value.toLowerCase().trim();
                  const variationName = variation.toLowerCase().trim();

                  if (
                    physicianName.includes(variationName) ||
                    variationName.includes(physicianName)
                  ) {
                    matchedPhysician = physician;
                    break;
                  }
                }

                if (matchedPhysician) break;
              }

              // If still no match, try matching by first name or last name
              if (!matchedPhysician && autoSelectPhysician.includes(' ')) {
                const nameParts = autoSelectPhysician.split(' ');
                const firstName = nameParts[0].toLowerCase().trim();
                const lastName = nameParts[nameParts.length - 1]
                  .toLowerCase()
                  .trim();

                for (const physician of appt) {
                  const physicianName = physician.value.toLowerCase();

                  if (
                    physicianName.includes(firstName) ||
                    physicianName.includes(lastName)
                  ) {
                    matchedPhysician = physician;
                    break;
                  }
                }
              }

              // If still no match, try the original partial match logic
              if (!matchedPhysician) {
                matchedPhysician = appt.find(
                  p =>
                    p.value
                      .toLowerCase()
                      .includes(autoSelectPhysician.toLowerCase()) ||
                    autoSelectPhysician
                      .toLowerCase()
                      .includes(p.value.toLowerCase()),
                );
              }
            }
          }

          if (matchedPhysician) {
            // Auto-select the physician by setting doctor_name
            setDetails(prevDetails => ({
              ...prevDetails,
              doctor_name: matchedPhysician.value,
            }));

            // Now that auto-population is complete, enable the dropdown so users can change the physician later if needed
            setTimeout(() => {
              set_PhyName(false);

              // Now that auto-population is complete, hide the physician loader
              setPhysicianLoader(false);

              // Enable the location field now that auto-population is complete
              setIsAutoPopulationInProgress(false);
              clearTimeout(handlePhyNameTimeout);
            }, 500);

            // Set the changing physician state to true to show loading for dates
            setChangingPhysician(true);

            // Show loading indicator for dates
            setDateLoader(true);

            // Log the updated details for debugging
            setTimeout(() => {}, 0);

            // Don't show the popup since we've auto-selected a physician
            setAppPopup(false);
          } else {
            // Enable the dropdown for manual selection when no match is found
            set_PhyName(false);

            // Only show popup for initial auto-population, not for manual changes
            if (isAutoPopulateTrigger && !isManualChange) {
              // Show the popup for manual selection since no match was found
              setAppPopup(true);
            } else {
            }

            // Clear the timeout and hide the physician loader since no match was found
            setPhysicianLoader(false);
            setIsAutoPopulationInProgress(false);
            clearTimeout(handlePhyNameTimeout);
          }
        } else {
          // Enable the dropdown for manual selection
          set_PhyName(false);

          // Only show popup for initial auto-population, not for manual changes
          if (isAutoPopulateTrigger && !isManualChange) {
            // Show the popup for manual selection since no physician was auto-selected
            setAppPopup(true);
          } else {
          }

          // Clear the timeout and hide the physician loader since no auto-selection was attempted
          setPhysicianLoader(false);
          setIsAutoPopulationInProgress(false);
          clearTimeout(handlePhyNameTimeout);
        }
      }

      // Set the physician list for the dropdown without an empty placeholder

      // Don't add an empty placeholder - just use the physician list as is
      setPhyList(appt);

      // Only hide the physician loader if we're not auto-selecting a physician
      // This ensures the loader remains visible until auto-selection is complete
      if (!autoSelectPhysician && !autoSelectPhysicianId) {
        setPhysicianLoader(false);

        // Enable the location field when no auto-selection is in progress
        setIsAutoPopulationInProgress(false);
        clearTimeout(handlePhyNameTimeout);
      } else {
      }
    } catch (error) {
      console.error('Error in HandlePhyName:', error);
      // Hide the physician loader if there's an error
      setPhysicianLoader(false);

      // Enable the dropdown for manual selection
      set_PhyName(false);

      // Enable the location field if there was an error during auto-population
      setIsAutoPopulationInProgress(false);
      clearTimeout(handlePhyNameTimeout);
    }
  };
  const HandleLocName = async (locationArr = []) => {
    const data = locationArr.map(item => {
      return {
        label: item.full_location_name,
        id: item.loc_id,
        value: item.location_name,
      };
    });
    setLocList(data);
  };

  function convertTo12HourFormat(timeString) {
    var time = new Date('2000-01-01T' + timeString);
    var hours = time.getHours();
    var minutes = time.getMinutes();
    var meridiem = hours < 12 ? 'AM' : 'PM';
    hours = hours > 12 ? hours - 12 : hours;
    hours = hours == 0 ? 12 : hours;
    return hours + ':' + (minutes < 10 ? '0' : '') + minutes + ' ' + meridiem;
  }

  const HandleApptDates = async (provider, slotdata) => {
    // Clear any existing appointment dates immediately and set empty array
    // This ensures no old dates are shown while loading
    setAvailabilityDate([]);

    // Show loading indicator for dates
    setDateLoader(true);

    // Clear time slots when dates are being loaded
    setavailableTimeSlot([]);

    try {
      // Trim provider name to handle any extra spaces
      const trimmedProvider = provider.trim();

      // Check if we have data for this provider
      if (!slotdata[trimmedProvider]) {
        // Try to find a close match
        const providerKeys = Object.keys(slotdata);
        const matchedProvider = providerKeys.find(
          key =>
            key.toLowerCase().includes(trimmedProvider.toLowerCase()) ||
            trimmedProvider.toLowerCase().includes(key.toLowerCase()),
        );

        if (matchedProvider) {
          const data = slotdata[matchedProvider];

          let dates = [];
          for (let i in data) {
            dates.push({
              label: ApptFormatDate(i),
              value: i,
            });
          }

          setAvailabilityDate(dates);
          setDateLoader(false);
          return;
        }

        // If no match found, try to fetch slots for this provider
        try {
          const appointmentType = details.appointment_purpose;

          if (!appointmentType) {
            setAvailabilityDate([]);
            setDateLoader(false);
            return;
          }

          // Find the physician ID
          const physician = phyList.find(
            p =>
              p.value.toLowerCase().trim() ===
              trimmedProvider.toLowerCase().trim(),
          );

          if (!physician || !physician.id) {
            setAvailabilityDate([]);
            setDateLoader(false);
            return;
          }

          // Fetch slots for this physician

          const res = await axiosPrivate.get(
            `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${pat_type}&appt_type_id=${appointmentType}&provider_id=${physician.id}`,
            {
              indication: 'Newappointment',
            },
          );

          if (res.data && Array.isArray(res.data) && res.data.length > 0) {
            // Organize the slots
            const newSlotData = organizeAppointments(res.data);

            // Get the provider name from the response
            const providerName = Object.keys(newSlotData)[0];

            // Update the openSlots state with the new data
            // Make sure to preserve the existing structure
            setOpenSlots(prevSlots => {
              return {
                ...prevSlots,
                [providerName]: newSlotData[providerName],
              };
            });

            // Get the dates from the new data
            const newData = newSlotData[providerName];

            let dates = [];
            for (let i in newData) {
              dates.push({
                label: ApptFormatDate(i),
                value: i,
              });
            }

            setAvailabilityDate(dates);
            setDateLoader(false);
            return;
          } else {
            setAvailabilityDate([]);
            setDateLoader(false);

            // Show error modal with "no slots available" message
            setErrorModal(true);
            setresponseErrorMsg(
              'No slots are available for this physician. Please select a different physician or appointment type.',
            );

            // Disable the physician dropdown when no slots are available
            set_PhyName(true);

            return;
          }
        } catch (error) {
          setAvailabilityDate([]);
          setDateLoader(false);
          return;
        }
      }

      // If we have data for this provider, use it
      const data = slotdata[trimmedProvider];

      let dates = [];
      for (let i in data) {
        dates.push({
          label: ApptFormatDate(i),
          value: i,
        });
      }

      setAvailabilityDate(dates);
    } catch (error) {
      setAvailabilityDate([]);
    } finally {
      // Hide loading indicator
      setDateLoader(false);

      // Set the changing physician state to false
      // This will re-enable the date dropdown
      setChangingPhysician(false);
    }
  };

  useEffect(() => {
    const willShowSubscription = Keyboard.addListener(
      'keyboardWillShow',
      () => {
        setKeyboardOpened(true);
      },
    );

    const didShowSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardOpened(true);
    });

    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardOpened(false);
    });

    return () => {
      willShowSubscription.remove();
      didShowSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  useEffect(() => {
    (async () => {
      let praticeInfoTemp = await GetPracticeInfo();

      setPreferredPrac(praticeInfoTemp.preferred_practice);
    })();

    // Cleanup function to ensure location field is enabled when component unmounts
    return () => {
      setIsAutoPopulationInProgress(false);
    };
  }, []);

  // Only load the physician list after both locationId and appointmentType are set
  useEffect(() => {
    (async () => {
      // Only proceed if we have both locationId and appointmentType
      if (locationId && preferredPrac && appointmentType) {
        // Don't show the physician loader here - it will be shown when getOpenSlots is called
        // This prevents the "Loading physicians..." message from showing prematurely
        // The actual physician loading will be handled by getOpenSlots when appointment type is selected
      } else if (locationId && preferredPrac && !appointmentType) {
        // Clear the physician list when location changes but appointment type is not selected
        setPhyList([]);
        // Hide the physician loader
        setPhysicianLoader(false);
      }
    })();
  }, [locationId, preferredPrac, appointmentType]);

  useEffect(() => {
    (async () => {
      if (details.doctor_name) {
        // Set the changing physician state to true
        // This will completely disable the date dropdown
        setChangingPhysician(true);

        // Clear any existing appointment dates immediately
        // Set to empty array to ensure no old dates are shown
        setAvailabilityDate([]);
        setavailableTimeSlot([]);

        // Enable loading state to disable the dropdown
        setDateLoader(true);

        // Add a small delay to ensure the UI updates before fetching new dates
        // This is crucial to ensure the old dates are cleared from the UI
        await new Promise(resolve => setTimeout(resolve, 100));

        // Double-check that dates are cleared
        setAvailabilityDate([]);

        // Check if we need to fetch new slots for this physician
        const trimmedProvider = details.doctor_name.trim();
        const appointmentType = details.appointment_purpose;

        if (appointmentType && !openSlots[trimmedProvider]) {
          try {
            // Find the physician ID
            const physician = phyList.find(
              p =>
                p.value.toLowerCase().trim() ===
                trimmedProvider.toLowerCase().trim(),
            );

            if (physician && physician.id) {
              // Fetch slots for this physician
              const res = await axiosPrivate.get(
                `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${pat_type}&appt_type_id=${appointmentType}&provider_id=${physician.id}`,
                {
                  indication: 'Newappointment',
                },
              );

              if (res.data && Array.isArray(res.data) && res.data.length > 0) {
                // Organize the slots
                const newSlotData = organizeAppointments(res.data);

                // Update the openSlots state
                setOpenSlots(prevSlots => {
                  // Log the current keys in openSlots

                  // Log the new keys being added

                  return {
                    ...prevSlots,
                    ...newSlotData,
                  };
                });

                // Now fetch the appointment dates with the updated slots
                setTimeout(() => {
                  HandleApptDates(details.doctor_name, newSlotData);
                }, 100);
              } else {
                setAvailabilityDate([]);
                setDateLoader(false);
                setChangingPhysician(false);

                // Show error modal with "no slots available" message
                setErrorModal(true);
                setresponseErrorMsg(
                  'No appointment slots available for this physician. Please select a different physician or appointment type.',
                );
              }
            } else {
              // Now fetch the appointment dates with the existing slots
              HandleApptDates(details.doctor_name, openSlots);
            }
          } catch (error) {
            setAvailabilityDate([]);
            setDateLoader(false);
            setChangingPhysician(false);
          }
        } else {
          // Now fetch the appointment dates with the existing slots

          HandleApptDates(details.doctor_name, openSlots);
        }

        // Fetch diagnosis items for this physician
        if (details.doctor_name !== 'Please call for assistance') {
          try {
            const diagnosisItems = await fetchDiagnosisItems(
              details.doctor_name,
            );

            // Store the diagnosis items in the state variable
            setDiagnosisList(diagnosisItems);
          } catch (error) {
            // Use default diagnosis items
            setDiagnosisList(getDefaultDiagnosisItems());
          }
        }
      } else {
        // Clear diagnosis list when no physician is selected
        setDiagnosisList([]);
      }
    })();
  }, [details.doctor_name, details.appointment_purpose]);

  const getApptTypeApiCall = pat_types => {
    setapptTypeLoader(true);
    return axiosPrivate
      .get(
        `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-appttypes?departmentid=${locationId}&patient_type=${pat_types}`,
      )
      .then(res => {
        return Promise.resolve(res.data);
      })
      .catch(err => {
        Promise.reject(err);
      });
  };

  const getAutoPopulateBlockType = async () => {
    try {
      setLoader(true);
      let pratice = await GetPracticeInfo();
      const res = await axiosPrivate.get(
        `${config[config.STAGING].PATIENT_INFO}${
          pratice.preferred_practice
        }/get_autopopulate_block_appt_types`,
      );

      setLoader(false);
      return Promise.resolve(res.data);
    } catch (e) {
      setLoader(false);
      setErrorModal(true);
      setresponseErrorMsg('An error occured while fetching Appointment Type');
      return Promise.reject('Error occured');
    }
  };

  useEffect(() => {
    (async () => {
      const res = await getAutoPopulateBlockType();
      setAutoPopulateBlockType(res);
    })();
  }, []);

  //slot handler for physician and slots
  //===========================================================
  const organizeAppointments = data => {
    const result = {};
    data.forEach(item => {
      // Ensure the displayname is properly formatted
      const displayname = item.provider.displayname || '';
      // Remove any trailing spaces to ensure consistent keys
      const fullName = displayname.trim();

      let appointments = [];
      item.slots.forEach(slot => {
        const appointmentDetails = {
          starttime: slot.starttime,
          appointmentid: slot.appointmentid,
          date: slot.date,
          appointmenttypeid: slot.appointmenttypeid,
        };
        appointments.push(appointmentDetails);
      });
      appointments = slotHandlerReshedule(appointments);

      // Store the appointments with the provider's full name as the key
      result[fullName] = appointments;
    });

    // Log the result for debugging

    return result;
  };

  const slotHandlerReshedule = data => {
    let timeSlots = {};
    data.forEach(slot => {
      if (!(slot.date in timeSlots)) {
        timeSlots[slot.date] = [
          `${slot.starttime}&${slot.appointmentid}&${slot.appointmenttypeid}`,
        ];
      } else {
        timeSlots[slot.date] = [
          ...timeSlots[slot.date],
          `${slot.starttime}&${slot.appointmentid}&${slot.appointmenttypeid}`,
        ];
      }
    });
    return timeSlots;
  };

  //===========================================================
  //handler slot in api
  useEffect(() => {
    const loadTimeSlots = async () => {
      if (details.appointment_date) {
        setavailableTimeSlot([]);
        setTimeSlotLoader(true);

        try {
          let provider = details.doctor_name.trim();
          let date = details.appointment_date;

          // Check if we have slots for this provider and date
          if (!openSlots[provider] || !openSlots[provider][date]) {
            // Try to find a close match for the provider
            const providerKeys = Object.keys(openSlots);
            const matchedProvider = providerKeys.find(
              key =>
                key.toLowerCase().includes(provider.toLowerCase()) ||
                provider.toLowerCase().includes(key.toLowerCase()),
            );

            if (matchedProvider && openSlots[matchedProvider][date]) {
              provider = matchedProvider;
            } else {
              // If we have a physician ID, try to fetch slots directly
              const physician = phyList.find(
                p =>
                  p.value.toLowerCase().trim() ===
                  provider.toLowerCase().trim(),
              );

              if (physician && physician.id && details.appointment_purpose) {
                try {
                  const res = await axiosPrivate.get(
                    `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${pat_type}&appt_type_id=${details.appointment_purpose}&provider_id=${physician.id}`,
                    {
                      indication: 'Newappointment',
                    },
                  );

                  if (
                    res.data &&
                    Array.isArray(res.data) &&
                    res.data.length > 0
                  ) {
                    // Organize the slots
                    const newSlotData = organizeAppointments(res.data);

                    // Update the openSlots state
                    setOpenSlots(prevSlots => {
                      // Log the current keys in openSlots

                      // Log the new keys being added

                      // Create a new object with the merged data
                      const mergedData = {
                        ...prevSlots,
                        ...newSlotData,
                      };

                      return mergedData;
                    });

                    // Try to get slots for the selected date
                    const providerName = Object.keys(newSlotData)[0];
                    if (
                      providerName &&
                      newSlotData[providerName] &&
                      newSlotData[providerName][date]
                    ) {
                      const newSlots = newSlotData[providerName][date];

                      // Format the time slots for the dropdown
                      const formattedSlots = newSlots.map(slot => ({
                        label: convertTo12HourFormat(slot.split('&')[0]),
                        value: slot,
                      }));

                      // Set the time slots
                      setavailableTimeSlot(formattedSlots);
                      setTimeSlotLoader(false);
                      return;
                    }
                  }
                } catch (fetchError) {}
              }

              setavailableTimeSlot([]);
              setTimeSlotLoader(false);
              return;
            }
          }

          // Get the slots for this provider and date
          let slots = openSlots[provider][date].map(item => {
            return {
              label: convertTo12HourFormat(item.split('&')[0]),
              value: item,
            };
          });

          if (slots && slots.length > 0) {
            setavailableTimeSlot(slots);
          } else {
            setavailableTimeSlot([]);
          }
        } catch (e) {
          setavailableTimeSlot([]);
        } finally {
          setTimeSlotLoader(false);
        }
      }
    };

    loadTimeSlots();
  }, [apptDate, details.appointment_date, details.doctor_name, openSlots]);

  useEffect(() => {
    (async () => {
      if (locationId !== '') {
        setPurList([]);
        setPhyList([]);
        setAvailabilityDate([]);
        setavailableTimeSlot([]);

        // Get the patient type
        let patientType = await getneworeshtablished();

        // Make sure we have a valid patient type
        if (!patientType) {
          patientType = 'Established patient';
          setpat_type(patientType);
        }

        // Get appointment types for this patient type
        let appointmentType = await getApptTypeApiCall(patientType);

        setapptTypeLoader(false);

        if (
          appointmentType &&
          Array.isArray(appointmentType) &&
          appointmentType.length > 0
        ) {
          const convertedApptTypeData = appointmentType.map(element => {
            return {
              value: element.ui_dropdown,
              label: element.ui_dropdown,
            };
          });
          setPurList(convertedApptTypeData);
        } else {
          setPurList([]);
        }
      }
    })();
  }, [locationId, preferredPrac]);

  // This function is called when appointment type is selected
  // It fetches available slots and physicians for the selected appointment type
  const getOpenSlots = async (appointmentType, type) => {
    if (appointmentType === '') return;

    // Set up a timeout to prevent infinite loading in getOpenSlots
    const getOpenSlotsTimeout = setTimeout(() => {
      console.log('getOpenSlots timeout reached, clearing loaders');
      setPhysicianLoader(false);
      setIsAutoPopulationInProgress(false);
      set_PhyName(false); // Enable dropdown for manual selection
    }, 12000); // 12 second timeout

    try {
      // Clear previous data
      setPhyList([]);
      setAvailabilityDate([]);
      setavailableTimeSlot([]);

      // Update the appointment purpose in the details state
      // This is important for filtering physicians by appointment type
      setDetails(prevDetails => ({
        ...prevDetails,
        appointment_purpose: appointmentType,
      }));

      // Make sure we have a valid patient type
      if (!pat_type) {
        // Use a default value if patient type is empty
        setpat_type('Established patient');
      }

      // Show loader while matching physician, but only if we have an appointment type
      // This ensures the loader is only shown after appointment type is selected
      if (appointmentType) {
        setPhysicianLoader(true);
      } else {
      }

      if (!locationId || !preferredPrac) {
        setPhysicianLoader(false);
        clearTimeout(getOpenSlotsTimeout);
        return;
      }

      // For manual appointment type changes, we want to skip auto-population
      // and just show the physicians without auto-selecting
      const isManualChange = type === 'manual_change';

      // Check if auto-population should be blocked for this appointment type
      const shouldBlockAutoPopulation =
        isManualChange || // Always block auto-population for manual changes
        autoPopulateBlockType.includes(appointmentType) ||
        (lastAppointmentData && lastAppointmentData.shouldBlockAutoPopulate) ||
        hasAutoPopulatedPhysician; // Block auto-population if we've already done it once

      const res = await openSlotApiCall(appointmentType, type);

      // For manual appointment type changes, we want to handle differently
      if (type === 'manual_change') {
        // Check if we have a valid response
        if (!Array.isArray(res.response) || res.response.length === 0) {
          // Hide the physician loader
          setPhysicianLoader(false);

          // Show the DefaultModal with "no slots available" message
          setAppPopup(true);

          // Set a custom message for the DefaultModal
          setNoSlotsMessage(
            'No slots are available for this appointment type. Please select a different appointment type or call for assistance.',
          );

          // Disable the physician dropdown when no slots are available
          set_PhyName(true);
          set_AptDate(true);
          set_AptSlot(true);

          return;
        }

        // Process the response as normal but skip auto-population
        const apptData = organizeAppointments(res.response);

        // Update the openSlots state - IMPORTANT: Merge with existing slots, don't replace
        setOpenSlots(prevSlots => {
          // Log the current keys in openSlots

          // Log the new keys being added

          // Create a new object with the merged data
          const mergedData = {
            ...prevSlots,
            ...apptData,
          };

          return mergedData;
        });

        // Set up the physician dropdown without auto-selection

        // Enable all fields for selection
        set_PhyName(false);
        set_AptDate(false);
        set_AptSlot(false);

        // Now that auto-population is complete, hide the physician loader
        setTimeout(() => {
          setPhysicianLoader(false);
        }, 500);

        await HandlePhyName(apptData, null);
        return;
      } else if (type === 'autopop') {
        if (!Array.isArray(res.response)) {
          throw new Error('Invalid response format: response is not an array');
        }

        const minAgeLimit = rsi_segAPI?.mapping_data?.age_mapping || {};

        // Filter providers by age if needed
        const filteredProviders = filterProvidersByAge(
          res.response,
          dob,
          minAgeLimit,
        );

        // Organize appointment data
        const apptData = organizeAppointments(filteredProviders);

        // Set the open slots - IMPORTANT: Merge with existing slots, don't replace
        setOpenSlots(prevSlots => {
          // Log the current keys in openSlots

          // Log the new keys being added

          // Create a new object with the merged data
          const mergedData = {
            ...prevSlots,
            ...apptData,
          };

          return mergedData;
        });

        // Log the available providers in the organized data

        // Get the physician to auto-select from the response
        const physicianToAutoSelect = res.providername;

        // Log the physician name for debugging
        if (physicianToAutoSelect) {
        }

        // Check if we have any appointment data
        if (Object.keys(apptData).length === 0) {
          // Hide the physician loader
          setPhysicianLoader(false);

          // Show the DefaultModal with "no slots available" message
          setAppPopup(true);

          // Set a custom message for the DefaultModal
          setNoSlotsMessage(
            'No slots are available for this appointment type. Please select a different appointment type or call for assistance.',
          );

          // Disable the physician dropdown when no slots are available
          set_PhyName(true);
          set_AptDate(true);
          set_AptSlot(true);

          return;
        }

        // Check if we should block auto-population for this appointment type
        if (shouldBlockAutoPopulation) {
          // Show the popup for manual selection
          setAppPopup(true);
          // Set up the dropdown without auto-selection
          await HandlePhyName(apptData, null, null);
        } else if (physicianToAutoSelect || res.providerId) {
          // Only auto-select if we have a valid physician to auto-select

          // Check if the physician exists in the data
          const physicianNames = Object.keys(apptData);

          // Try to find an exact match first
          let matchedPhysician = physicianNames.find(
            name =>
              name.toLowerCase().trim() ===
              physicianToAutoSelect.toLowerCase().trim(),
          );

          // If no exact match, try a partial match
          if (!matchedPhysician) {
            matchedPhysician = physicianNames.find(
              name =>
                name
                  .toLowerCase()
                  .includes(physicianToAutoSelect.toLowerCase()) ||
                physicianToAutoSelect
                  .toLowerCase()
                  .includes(name.toLowerCase()),
            );
          }

          // If still no match, try matching just the first name or last name
          if (!matchedPhysician && physicianToAutoSelect.includes(' ')) {
            const nameParts = physicianToAutoSelect.split(' ');
            const firstName = nameParts[0].toLowerCase().trim();
            const lastName = nameParts[nameParts.length - 1]
              .toLowerCase()
              .trim();

            matchedPhysician = physicianNames.find(name => {
              const nameLower = name.toLowerCase();
              return (
                nameLower.includes(firstName) || nameLower.includes(lastName)
              );
            });
          }

          if (matchedPhysician) {
            // Get the physician ID from the response
            const physicianId = res.providerId;

            // Auto-select the physician
            await HandlePhyName(apptData, physicianToAutoSelect, physicianId);

            // Force an update to ensure the doctor_name is set
            setTimeout(() => {
              // Double-check if the doctor_name was set
              if (!details.doctor_name) {
                setDetails(prevDetails => ({
                  ...prevDetails,
                  doctor_name: matchedPhysician,
                }));

                // Now that auto-population is complete, enable the dropdown so users can change the physician later if needed
                setTimeout(() => {
                  set_PhyName(false);
                }, 500);
              }
            }, 100);

            // Don't show the popup since we've auto-selected a physician
            setAppPopup(false);
          } else {
            // Show the popup for manual selection since no match was found
            setAppPopup(true);

            // Now that auto-population is complete, enable the dropdown for manual selection
            setTimeout(() => {
              set_PhyName(false);

              // Now that auto-population is complete, hide the physician loader
              setPhysicianLoader(false);
            }, 500);

            await HandlePhyName(apptData, null, null);
          }
        } else {
          // Show the popup for manual selection since no physician was provided
          setAppPopup(true);

          // Now that auto-population is complete, enable the dropdown for manual selection
          setTimeout(() => {
            set_PhyName(false);

            // Now that auto-population is complete, hide the physician loader
            setPhysicianLoader(false);
          }, 500);

          await HandlePhyName(apptData, null, null);
        }

        // If we have a physician name, set up available dates
        if (details.doctor_name) {
          HandleApptDates(details.doctor_name, apptData);
        } else if (physicianToAutoSelect) {
          // Find the physician in the available list
          const physicianNames = Object.keys(apptData);
          const matchedPhysician = physicianNames.find(name =>
            name.toLowerCase().includes(physicianToAutoSelect.toLowerCase()),
          );

          if (matchedPhysician) {
            // Set the doctor_name in the details state
            setDetails(prevDetails => ({
              ...prevDetails,
              doctor_name: matchedPhysician,
            }));

            // Now that auto-population is complete, enable the dropdown so users can change the physician later if needed
            setTimeout(() => {
              set_PhyName(false);

              // Now that auto-population is complete, hide the physician loader
              setPhysicianLoader(false);
            }, 500);

            // Now fetch the dates for this physician
            HandleApptDates(matchedPhysician, apptData);
          }
        }
      } else {
        // For normal (non-autopop) selection
        const apptData = organizeAppointments(res);
        setOpenSlots(apptData);

        if (Object.keys(apptData).length === 0) {
          // Hide the physician loader
          setPhysicianLoader(false);

          // Show the DefaultModal with "no slots available" message
          setAppPopup(true);

          // Set a custom message for the DefaultModal
          setNoSlotsMessage(
            'No slots are available for this appointment type. Please select a different appointment type or call for assistance.',
          );

          // Disable the physician dropdown when no slots are available
          set_PhyName(true);
          set_AptDate(true);
          set_AptSlot(true);

          return;
        }

        // Set up the physician dropdown without auto-selection but make it enabled

        // Don't show the popup for manual selection when not in auto-populate mode
        setAppPopup(false);
        // Now that auto-population is complete, enable the dropdown for manual selection
        setTimeout(() => {
          set_PhyName(false);

          // Now that auto-population is complete, hide the physician loader
          setPhysicianLoader(false);

          // Enable the location field now that auto-population is complete
          setIsAutoPopulationInProgress(false);
        }, 500);

        await HandlePhyName(apptData, null);
      }
    } catch (error) {
      console.error('Error in getOpenSlots:', error);
      // Hide the physician loader if there's an error
      setPhysicianLoader(false);

      // Show the DefaultModal with error message
      setAppPopup(true);

      // Set a custom message for the DefaultModal
      setNoSlotsMessage(
        'An error occurred while fetching physicians. Please try again or call for assistance.',
      );

      // Enable the dropdown for manual selection
      set_PhyName(false);

      // Enable the location field if there was an error during auto-population
      setIsAutoPopulationInProgress(false);
      clearTimeout(getOpenSlotsTimeout);
    } finally {
      // Always make sure the location field is enabled when we're done
      setIsAutoPopulationInProgress(false);
      clearTimeout(getOpenSlotsTimeout);
    }
  };

  const filterProvidersByAge = (providerList, dob, minAgeLimit) => {
    const ageMapping = minAgeLimit?.age_mapping;
    if (!ageMapping || Object.keys(ageMapping).length === 0) {
      return providerList;
    }

    const age = moment().diff(moment(dob, 'YYYY-MM-DD'), 'years');

    const filtered = providerList.filter(item => {
      const providerId = item.provider?.providerid;
      if (!providerId) return false;

      const rule = ageMapping[providerId];
      if (!rule) return true;

      const {operator, start_age} = rule;

      try {
        return new Function('age', `return age ${operator} ${start_age}`)(age);
      } catch (error) {
        return true;
      }
    });

    return filtered;
  };

  // Function to fetch diagnosis items for a physician
  const fetchDiagnosisItems = async physicianName => {
    try {
      // Get practice info
      let practice = await GetPracticeInfo();

      // Get the diagnosis mapping data
      const diagnosisMapping =
        rsi_segAPI?.mapping_data?.diagnosis_mapping || {};

      // Find the physician ID from the phyList

      const physician = phyList.find(
        p =>
          p.value.toLowerCase().trim() === physicianName.toLowerCase().trim(),
      );

      const physicianId = physician?.id;

      if (!physicianId) {
        const defaultItems = getDefaultDiagnosisItems();

        return defaultItems;
      }

      // If we have diagnosis mapping data, use it to filter by provider ID
      if (Object.keys(diagnosisMapping).length > 0) {
        const filteredDiagnoses = Object.entries(diagnosisMapping)
          .filter(([_, data]) => {
            const includes =
              data?.provider?.includes(parseInt(physicianId)) ||
              data?.provider?.includes(physicianId);

            return includes;
          })
          .map(([diagnosis], index) => ({
            label: diagnosis,
            value: diagnosis,
            key: index.toString(),
          }));

        if (filteredDiagnoses.length > 0) {
          return filteredDiagnoses;
        } else {
          // If no diagnoses found for this provider, try the API as fallback
        }
      }

      // Fallback to API if no mapping data or no matches in mapping
      // Fetch diagnosis items from the API
      const apiUrl = `${config[config.STAGING].PATIENT_INFO}${
        practice.preferred_practice
      }/diagnosis?provider_id=${physicianId}`;

      const res = await axiosPrivate.get(apiUrl);

      if (res.data && Array.isArray(res.data) && res.data.length > 0) {
        // Format the diagnosis items for the dropdown
        const formattedDiagnosis = res.data.map((diagnosis, index) => ({
          label: diagnosis.name || diagnosis.diagnosis || diagnosis,
          value: diagnosis.name || diagnosis.diagnosis || diagnosis,
          key: index.toString(),
        }));

        return formattedDiagnosis;
      } else {
        const defaultItems = getDefaultDiagnosisItems();

        return defaultItems;
      }
    } catch (error) {
      const defaultItems = getDefaultDiagnosisItems();

      return defaultItems;
    }
  };

  // Function to get default diagnosis items
  const getDefaultDiagnosisItems = () => {
    // Use the diagnosis list from the mapping data if available
    const diagnosisList = rsi_segAPI?.mapping_data?.diagnosis || [];

    if (diagnosisList.length > 0) {
      const formattedList = diagnosisList.map((item, index) => ({
        label: item,
        value: item,
        key: index.toString(),
      }));

      return formattedList;
    }

    // Fallback to a default list

    const fallbackList = [
      {label: 'Annual Physical', value: 'Annual Physical'},
      {label: 'Follow-up', value: 'Follow-up'},
      {label: 'Consultation', value: 'Consultation'},
      {label: 'Preventive Care', value: 'Preventive Care'},
      {
        label: 'Chronic Disease Management',
        value: 'Chronic Disease Management',
      },
    ];

    return fallbackList;
  };

  // Function to get diagnosis items for the dropdown
  const getDiagnosisItems = providerDisplayName => {
    // If no provider is selected, return an empty list
    if (!providerDisplayName) {
      return [];
    }

    // Use the diagnosis mapping data if available
    const diagnosisMapping = rsi_segAPI?.mapping_data?.diagnosis_mapping || {};

    // If we have diagnosis mapping data, filter by provider ID
    if (Object.keys(diagnosisMapping).length > 0) {
      // Try to find the provider ID from phyList first (more reliable)
      const physician = phyList.find(
        p =>
          p.value.toLowerCase().trim() ===
          providerDisplayName.toLowerCase().trim(),
      );

      let providerId = physician?.id;

      // If not found in phyList, try to find in slotres as fallback
      if (!providerId) {
        const matchedProvider = slotres.find(
          item =>
            item?.provider?.displayname?.toLowerCase().trim() ===
            providerDisplayName?.toLowerCase().trim(),
        );
        providerId = matchedProvider?.provider?.providerid;
      }

      if (providerId) {
        const filteredDiagnoses = Object.entries(diagnosisMapping)
          .filter(([_, data]) => {
            const includes =
              data?.provider?.includes(parseInt(providerId)) ||
              data?.provider?.includes(providerId);

            return includes;
          })
          .map(([diagnosis], index) => ({
            label: diagnosis,
            value: diagnosis,
            key: index.toString(),
          }));

        if (filteredDiagnoses.length > 0) {
          return filteredDiagnoses;
        } else {
        }
      } else {
      }
    }

    // If we couldn't find the provider or diagnoses, return the default list

    return getDefaultDiagnosisItems();
  };

  // This function handles fetching slots and auto-populating physician based on last appointment
  // It should only be called after appointment type is selected
  const openSlotApiCall = async (appointmentType, type) => {
    try {
      // For manual appointment type changes, we want to skip auto-population
      // and just show the physicians without auto-selecting
      const isManualChange = type === 'manual_change';

      // Track whether this is an auto-populate trigger or manual selection
      const isAutoPopulateTrigger = type === 'autopop';

      // Show loading indicator for physicians only if we're in auto-population mode
      // or if we're manually changing the appointment type
      if (isAutoPopulateTrigger || isManualChange) {
        setPhysicianLoader(true);

        // Force a UI update to ensure the loader is shown
        setTimeout(() => {
          setPhysicianLoader(true);
        }, 0);
      }

      // Check if auto-population should be blocked for this appointment type
      const shouldBlockAutoPopulation =
        isManualChange || // Always block auto-population for manual changes
        (autoPopulateBlockType &&
          autoPopulateBlockType.includes(appointmentType));

      // Only proceed with physician matching if we have a valid appointment type
      if (!appointmentType) {
        return {response: response.data};
      }

      // Check if auto-population should be blocked based on lastAppointmentData
      if (lastAppointmentData && lastAppointmentData.shouldBlockAutoPopulate) {
      }

      // Update the appointment purpose in the details state
      // This is important for filtering physicians by appointment type
      setDetails(prevDetails => ({
        ...prevDetails,
        appointment_purpose: appointmentType,
      }));

      // Log the patient type - this is important for debugging
      if (!pat_type) {
      }

      // Make sure we have a valid patient type
      if (!pat_type) {
      }

      // Use the current patient type - no fallback
      const patientTypeToUse = pat_type || '';

      const response = await axiosPrivate.get(
        `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${patientTypeToUse}&appt_type_id=${appointmentType}`,
        {
          indication: 'Newappointment',
        },
      );

      // Store the response data for later use
      setSlotres(response.data);

      if (type === 'onchange') {
        // For normal selection, just return the data
        return response.data;
      } else {
        // For auto-population, try to match the provider from the last appointment
        if (
          !response.data ||
          !Array.isArray(response.data) ||
          response.data.length === 0
        ) {
          return {response: []};
        }

        const formattedData = response.data.map(item => ({
          displayName: item.provider.displayname + ' ',
          providerId: item.provider.providerid,
        }));

        // Determine which appointment list to use (upcoming or previous)
        let appointmentList = [];

        // If we have upcoming appointments, use them
        if (upcomingAppointments.length > 0) {
          // For upcoming appointments, sort by date in descending order (newest first)
          appointmentList = [...upcomingAppointments].sort((a, b) => {
            // Convert date strings to Date objects for comparison
            // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
            let dateA, dateB;

            if (a.appt_date) {
              // Format: YYYY-MM-DD
              dateA = new Date(a.appt_date);
            } else if (a.date) {
              // Format: MM/DD/YYYY
              const parts = a.date.split('/');
              dateA = new Date(parts[2], parts[0] - 1, parts[1]);
            } else if (a.appointmentdate) {
              dateA = new Date(a.appointmentdate);
            }

            if (b.appt_date) {
              // Format: YYYY-MM-DD
              dateB = new Date(b.appt_date);
            } else if (b.date) {
              // Format: MM/DD/YYYY
              const parts = b.date.split('/');
              dateB = new Date(parts[2], parts[0] - 1, parts[1]);
            } else if (b.appointmentdate) {
              dateB = new Date(b.appointmentdate);
            }

            return dateB - dateA; // Descending order (newest first)
          });
        }
        // If no upcoming appointments, use previous appointments
        else if (previousAppointments && previousAppointments.length > 0) {
          // For previous appointments, sort by date in descending order (newest first)
          appointmentList = [...previousAppointments].sort((a, b) => {
            // Convert date strings to Date objects for comparison
            // Handle different date formats (YYYY-MM-DD or MM/DD/YYYY)
            let dateA, dateB;

            if (a.appt_date) {
              // Format: YYYY-MM-DD
              dateA = new Date(a.appt_date);
            } else if (a.date) {
              // Format: MM/DD/YYYY
              const parts = a.date.split('/');
              dateA = new Date(parts[2], parts[0] - 1, parts[1]);
            } else if (a.appointmentdate) {
              dateA = new Date(a.appointmentdate);
            }

            if (b.appt_date) {
              // Format: YYYY-MM-DD
              dateB = new Date(b.appt_date);
            } else if (b.date) {
              // Format: MM/DD/YYYY
              const parts = b.date.split('/');
              dateB = new Date(parts[2], parts[0] - 1, parts[1]);
            } else if (b.appointmentdate) {
              dateB = new Date(b.appointmentdate);
            }

            return dateB - dateA; // Descending order (newest first)
          });
          // Using previous appointments
        } else {
          return {response: response.data};
        }

        // Get the most recent appointment (first after sorting by date descending)
        const lastAppointment = appointmentList[0];

        // Log detailed information about the selected appointment

        // IMPORTANT: Only proceed with auto-population if ui_dropdown is not null/undefined
        if (!lastAppointment.ui_dropdown) {
          return {
            providername: null,
            response: response.data,
          };
        }

        // Check if this appointment has ui_dropdown values that should block auto-population
        if (lastAppointment.ui_dropdown && autoPopulateBlockType) {
          const shouldBlockBasedOnDropdown = autoPopulateBlockType.some(
            blockType => lastAppointment.ui_dropdown.includes(blockType),
          );

          if (shouldBlockBasedOnDropdown) {
            return {
              providername: null,
              response: response.data,
            };
          }
        }

        // Check if we should auto-select the physician from the last appointment
        // Only auto-select if auto-population is not blocked
        if (
          !shouldBlockAutoPopulation &&
          lastAppointment &&
          lastAppointment.providerid
        ) {
          // Find the physician in the available providers by provider ID
          let matchingProvider = formattedData.find(
            provider => provider.providerId === lastAppointment.providerid,
          );

          // Log the last appointment details for debugging

          // If no match by ID, try to match by name components
          if (
            !matchingProvider &&
            (lastAppointment.physician_fname || lastAppointment.physician_lname)
          ) {
            // Create variations of the name to try matching
            const nameVariations = [];

            // Add full name variations if both first and last name are available
            if (
              lastAppointment.physician_fname &&
              lastAppointment.physician_lname
            ) {
              nameVariations.push(
                `${lastAppointment.physician_fname} ${lastAppointment.physician_lname}`,
                `${lastAppointment.physician_fname} ${lastAppointment.physician_lname},`,
                `Dr. ${lastAppointment.physician_fname} ${lastAppointment.physician_lname}`,
                `${lastAppointment.physician_lname}, ${lastAppointment.physician_fname}`,
                lastAppointment.physician_lname,
              );
            }

            // Add first name and last name individually for more flexible matching
            if (lastAppointment.physician_fname) {
              nameVariations.push(lastAppointment.physician_fname);
            }

            if (lastAppointment.physician_lname) {
              nameVariations.push(lastAppointment.physician_lname);
            }

            // Try to find a match with any of the name variations
            for (const provider of formattedData) {
              const displayName = provider.displayName.toLowerCase().trim();

              for (const nameVariation of nameVariations) {
                const variationLower = nameVariation.toLowerCase().trim();
                if (
                  displayName.includes(variationLower) ||
                  variationLower.includes(displayName)
                ) {
                  matchingProvider = provider;
                  break;
                }
              }

              if (matchingProvider) break;
            }

            // If still no match, try a more aggressive approach with just the first name
            if (!matchingProvider && lastAppointment.physician_fname) {
              for (const provider of formattedData) {
                const displayName = provider.displayName.toLowerCase();
                const firstName = lastAppointment.physician_fname.toLowerCase();

                if (displayName.includes(firstName)) {
                  matchingProvider = provider;
                  break;
                }
              }
            }
          }

          if (matchingProvider) {
            // Log the exact name we're returning for auto-selection
            const physicianName = matchingProvider.displayName.trim();

            return {
              providername: physicianName,
              providerId: matchingProvider.providerId,
              response: response.data,
            };
          } else {
            // Try to construct the name from first and last name if available
            if (
              lastAppointment.physician_fname &&
              lastAppointment.physician_lname
            ) {
              const constructedName = `${lastAppointment.physician_fname} ${lastAppointment.physician_lname}`;

              // Check if any provider name contains this constructed name
              const nameMatchProvider = formattedData.find(provider =>
                provider.displayName
                  .toLowerCase()
                  .includes(constructedName.toLowerCase()),
              );

              if (nameMatchProvider) {
                return {
                  providername: nameMatchProvider.displayName.trim(),
                  providerId: nameMatchProvider.providerId,
                  response: response.data,
                };
              }
            }
          }
        }

        // If auto-population is blocked or no matching physician found, return null

        return {
          providername: null,
          response: response.data,
        };
      }
    } catch (err) {
      console.error('Error in openSlotApiCall:', err);
      // Hide the physician loader if there's an error
      setPhysicianLoader(false);

      // Enable the dropdown for manual selection
      set_PhyName(false);

      // Enable the location field if there was an error during auto-population
      setIsAutoPopulationInProgress(false);

      return Promise.reject(err);
    }
  };

  const validationLast = () => {
    if (
      details.appointment_date !== '' &&
      details.appointment_location !== '' &&
      details.appointment_purpose !== '' &&
      details.appointment_time !== '' &&
      details.doctor_name !== '' &&
      details.appointmentid !== '' &&
      details.diagnosis !== '' &&
      details.locationId !== ''
    )
      return false;
    return true;
  };

  function convertDateFormat(dateString) {
    var parts = dateString.split('/');
    var mm = parts[0];
    var dd = parts[1];
    var yyyy = parts[2];
    return yyyy + '-' + mm + '-' + dd;
  }

  const HandleCreateAppt = async () => {
    try {
      setBtnLoader(true);
      const practice = await GetPracticeInfo();
      delete userInfo.photo_base64;
      axiosPrivate
        .post(
          `${config[config.STAGING].PATIENT_INFO}${
            practice.preferred_practice
          }/book-appts`,
          {
            patient_id: practice[practice.preferred_practice],
            departmentid: details.locationId,
            appt_id: details.appointmentid,
            appt_type_id: details.appointment_time.split('&')[2],
            reason: details.note,
            userInfo: userInfo,
            appointment: details,
          },
        )
        .then(async () => {
          const data = details;
          data.appointment_date = convertDateFormat(data.appointment_date);
          setBtnLoader(false);
          setAsyncStorage('newAppt', new Date().toString());
          setAsyncStorage('newapttime', new Date().toString());
          setRequestModal(true);
        })
        .catch(err => {
          setBtnLoader(false);
          setresponseErrorMsg(err.message);
          setErrorModal(true);
        });
    } catch (e) {
      setBtnLoader(false);
      setresponseErrorMsg(err.message);
      setErrorModal(true);
    }
  };

  if (loader) {
    return (
      <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
        <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
      </View>
    );
  }

  return (
    <>
      <View style={s?.container}>
        <KeyboardAwareScrollView persistentScrollbar={true}>
          <Header
            backgroundColor
            onBackPress={() => navigation.goBack()}
            title="Request New Appointment"
            spaceRight
          />
          <View style={{alignItems: 'center'}}>
            <Image
              source={
                preferredPrac === 'rsi'
                  ? rsiImg
                  : preferredPrac === 'sen'
                    ? senImg
                    : ''
              }
              style={{resizeMode: 'cover'}}
            />
          </View>
          <ScrollView
            persistentScrollbar={true}
            style={{
              flex: 1,
              // paddingVertical: 5
            }}
            contentContainerStyle={s?.appoinymentsBlock}
            keyboardShouldPersistTaps="handled">
            {/* location */}
            <View
              style={[
                isAutoPopulationInProgress ? s.parentDisabled : s.parentEnabled,
                {width: '100%'},
              ]}>
              <OurTextInput
                value={''}
                placeholder={''}
                editable={false}
                title="Location"
                bgColor={theme.colors.brandBackground}
                customHeight={80}
                paddingCustomLeft={0}
                paddingCustomRight={0}
                onPress={() => {}}
                validationOk={true}
              />
              <View style={s?.dataPicker}>
                <DataPicker
                  items={locList}
                  placeholder={
                    isAutoPopulationInProgress
                      ? 'Auto-populating location...'
                      : 'Select Location'
                  }
                  title={'Select Location'}
                  onValueChange={value => {
                    const getLocationId = locList.filter(
                      item => item.value === value,
                    )[0];
                    setLocationId(getLocationId.id);
                    setDetails({
                      ...details,
                      appointment_date: '',
                      appointment_purpose: '',
                      appointment_time: '',
                      appointmentType: '',
                      doctor_name: '',
                      appointment_location: value,
                      locationId: getLocationId.id,
                      diagnosis: '',
                    });
                    set_SelectLocation(false);
                  }}
                  disabled={isAutoPopulationInProgress}
                  valuePicked={details.appointment_location}
                  DropIcon={isAutoPopulationInProgress ? false : true}
                />
                {(physicianDetails.isFetching ||
                  isAutoPopulationInProgress) && (
                  <ActivityIndicator
                    style={{paddingHorizontal: 10}}
                    color={theme.colors.brandPrimary}
                    size={isAutoPopulationInProgress ? 'large' : 'small'}
                  />
                )}
              </View>
            </View>

            {/* appointment type */}
            <View
              style={[
                purList.length === 0 ? s.parentDisabled : s.parentEnabled,
                {width: '100%'},
              ]}>
              <OurTextInput
                value={''}
                placeholder={''}
                editable={false}
                title="Appointment type"
                bgColor={theme.colors.brandBackground}
                customHeight={80}
                paddingCustomLeft={0}
                paddingCustomRight={0}
                onPress={() => {}}
                validationOk={true}
              />

              <View style={s?.dataPicker}>
                <DataPicker
                  items={purList}
                  title="Select appointment type"
                  placeholder="Select appointment type"
                  onValueChange={value => {
                    setAppointmentType(value);

                    // Clear previous physician data
                    setPhyList([]);
                    setAvailabilityDate([]);
                    setavailableTimeSlot([]);

                    // Update details with the new appointment type
                    setDetails({
                      ...details,
                      appointment_date: '',
                      appointment_time: '',
                      appointment_purpose: value,
                      doctor_name: '',
                    });

                    // Show physician loader when appointment type is selected
                    setPhysicianLoader(true);

                    // Determine if this is a manual change or first selection
                    if (hasAutoPopulatedPhysician) {
                      // Get open slots for this appointment type with manual_change flag
                      // This will prevent auto-population
                      getOpenSlots(value, 'manual_change');
                    } else {
                      // Check if we have previous appointments to auto-populate from
                      const hasAppointmentsToAutoPopulateFrom =
                        (upcomingAppointments &&
                          upcomingAppointments.length > 0) ||
                        (previousAppointments &&
                          previousAppointments.length > 0);

                      if (hasAppointmentsToAutoPopulateFrom) {
                        // Check if the most recent appointment has a valid ui_dropdown value
                        let shouldAutoPopulate = false;
                        let mostRecentAppointment = null;

                        // Get the most recent appointment from either upcoming or previous
                        if (
                          upcomingAppointments &&
                          upcomingAppointments.length > 0
                        ) {
                          // Sort upcoming appointments by date (newest first)
                          const sortedUpcoming = [...upcomingAppointments].sort(
                            (a, b) => {
                              let dateA, dateB;
                              if (a.appt_date) {
                                dateA = new Date(a.appt_date);
                              } else if (a.date) {
                                const parts = a.date.split('/');
                                dateA = new Date(
                                  parts[2],
                                  parts[0] - 1,
                                  parts[1],
                                );
                              } else if (a.appointmentdate) {
                                dateA = new Date(a.appointmentdate);
                              }

                              if (b.appt_date) {
                                dateB = new Date(b.appt_date);
                              } else if (b.date) {
                                const parts = b.date.split('/');
                                dateB = new Date(
                                  parts[2],
                                  parts[0] - 1,
                                  parts[1],
                                );
                              } else if (b.appointmentdate) {
                                dateB = new Date(b.appointmentdate);
                              }

                              return dateB - dateA; // Descending order (newest first)
                            },
                          );
                          mostRecentAppointment = sortedUpcoming[0];
                        } else if (
                          previousAppointments &&
                          previousAppointments.length > 0
                        ) {
                          // Sort previous appointments by date (newest first)
                          const sortedPrevious = [...previousAppointments].sort(
                            (a, b) => {
                              let dateA, dateB;
                              if (a.appt_date) {
                                dateA = new Date(a.appt_date);
                              } else if (a.date) {
                                const parts = a.date.split('/');
                                dateA = new Date(
                                  parts[2],
                                  parts[0] - 1,
                                  parts[1],
                                );
                              } else if (a.appointmentdate) {
                                dateA = new Date(a.appointmentdate);
                              }

                              if (b.appt_date) {
                                dateB = new Date(b.appt_date);
                              } else if (b.date) {
                                const parts = b.date.split('/');
                                dateB = new Date(
                                  parts[2],
                                  parts[0] - 1,
                                  parts[1],
                                );
                              } else if (b.appointmentdate) {
                                dateB = new Date(b.appointmentdate);
                              }

                              return dateB - dateA; // Descending order (newest first)
                            },
                          );
                          mostRecentAppointment = sortedPrevious[0];
                        }

                        // Check if the most recent appointment has a valid ui_dropdown value
                        if (
                          mostRecentAppointment &&
                          mostRecentAppointment.ui_dropdown
                        ) {
                          shouldAutoPopulate = true;
                        } else {
                          shouldAutoPopulate = false;
                        }

                        if (shouldAutoPopulate) {
                          // Set the flag to indicate we've tried auto-population
                          setHasAutoPopulatedPhysician(true);

                          // Get open slots for this appointment type with autopop flag
                          // This will try to auto-populate the physician
                          getOpenSlots(value, 'autopop');
                        } else {
                          // Get open slots for this appointment type with manual_change flag
                          // This will prevent auto-population
                          getOpenSlots(value, 'manual_change');
                        }
                      } else {
                        // Get open slots for this appointment type with manual_change flag
                        // This will prevent auto-population
                        getOpenSlots(value, 'manual_change');
                      }
                    }

                    // Enable all fields for selection
                    set_PhyName(false);
                    set_AptDate(false);
                    set_AptSlot(false);

                    // Force enable fields again after a short delay to ensure they're enabled
                    setTimeout(() => {
                      set_PhyName(false);
                      set_AptDate(false);
                      set_AptSlot(false);
                    }, 500);

                    setPhyList([]);
                    setAvailabilityDate([]);
                    setavailableTimeSlot([]);
                  }}
                  valuePicked={details.appointment_purpose}
                  DropIcon={apptTypeLoader ? false : true}
                  disabled={purList.length === 0}
                />

                {apptTypeLoader && (
                  <ActivityIndicator
                    style={{paddingHorizontal: 10}}
                    color={theme.colors.brandPrimary}
                  />
                )}
              </View>
            </View>
            {/* physician */}
            <View style={{width: '100%', flexDirection: 'column'}}>
              <View
                style={[
                  // Use parentDisabled when the dropdown should be disabled
                  // Also disable when no appointment type is selected
                  phy_name || !details.appointment_purpose
                    ? s.parentDisabled
                    : s.parentEnabled,
                  {width: '100%'},
                ]}>
                <OurTextInput
                  value={''}
                  placeholder={''}
                  editable={false}
                  title="Physician Name"
                  bgColor={theme.colors.brandBackground}
                  customHeight={80}
                  paddingCustomLeft={0}
                  paddingCustomRight={0}
                  borderRadius={8}
                  onPress={() => {}}
                  validationOk={true}
                />
                <View style={s?.dataPicker}>
                  <DataPicker
                    items={phyList}
                    title={'Select Physician'}
                    placeholder={
                      physicianLoader
                        ? 'Loading physicians...'
                        : 'Select Physician'
                    }
                    onValueChange={async value => {
                      // Skip if empty value is selected (the "Select Physician" placeholder)
                      if (!value) {
                        return;
                      }

                      // This is a manual physician change, so we don't need to show the "no matching physician" modal
                      // We'll handle any errors with the appropriate error message

                      // Set the changing physician state to true
                      // This will completely disable the date dropdown
                      setChangingPhysician(true);

                      // Immediately clear appointment dates and show loader
                      // Set to empty array to ensure no old dates are shown
                      setAvailabilityDate([]);

                      // Enable loading state to disable the dropdown
                      setDateLoader(true);

                      // Clear time slots
                      setavailableTimeSlot([]);

                      // Force a UI update to show the loading state
                      setTimeout(() => {
                        setDateLoader(true);
                      }, 0);

                      // Log for debugging

                      // If phyList is empty, physicianData will be undefined
                      // In that case, we'll just set the doctor_name without an ID
                      const physicianData = phyList.find(
                        item => item.value === value,
                      );

                      if (physicianData && physicianData.id) {
                        setPhysicianId(physicianData.id);
                      }

                      // Update the details state with the selected physician
                      setDetails({
                        ...details,
                        doctor_name: value,
                        appointment_date: '',
                        appointment_time: '',
                        diagnosis: '', // Clear diagnosis when physician changes
                      });
                      set_PhyName(false);

                      // If the user selects "Please call for assistance", show the popup
                      if (value === 'Please call for assistance') {
                        setAppPopup(true);
                        return;
                      }

                      // This is a manual physician change, mark that we've already auto-populated
                      // to prevent auto-population from happening again
                      setHasAutoPopulatedPhysician(true);

                      // Fetch diagnosis items for this physician
                      try {
                        const diagnosisItems = await fetchDiagnosisItems(value);

                        // Store the diagnosis items in a state variable
                        setDiagnosisList(diagnosisItems);
                      } catch (error) {
                        // Use default diagnosis items
                        setDiagnosisList(getDefaultDiagnosisItems());
                      }

                      // Fetch appointment dates for this physician
                      try {
                        // Add a small delay to ensure the UI updates before fetching new dates
                        // This is crucial to ensure the old dates are cleared from the UI
                        await new Promise(resolve => setTimeout(resolve, 100));

                        // Double-check that dates are cleared
                        setAvailabilityDate([]);

                        // If we have openSlots data, use it
                        if (openSlots && Object.keys(openSlots).length > 0) {
                          await HandleApptDates(value, openSlots);
                        } else {
                          // If we don't have openSlots data, fetch it
                          const appointmentType = details.appointment_purpose;

                          if (!appointmentType) {
                            return;
                          }

                          // Fetch slots for this physician
                          const res = await axiosPrivate.get(
                            `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${pat_type}&appt_type_id=${appointmentType}&provider_id=${physicianData?.id || ''}`,
                            {
                              indication: 'Newappointment',
                            },
                          );

                          if (
                            res.data &&
                            Array.isArray(res.data) &&
                            res.data.length > 0
                          ) {
                            // Organize the slots
                            const newSlotData = organizeAppointments(res.data);

                            // Update the openSlots state - IMPORTANT: Merge with existing slots, don't replace
                            setOpenSlots(prevSlots => {
                              // Log the current keys in openSlots

                              // Log the new keys being added

                              // Create a new object with the merged data
                              const mergedData = {
                                ...prevSlots,
                                ...newSlotData,
                              };

                              return mergedData;
                            });

                            // Set up the dates
                            await HandleApptDates(value, newSlotData);
                          } else {
                            setAvailabilityDate([]);

                            // Show error modal with "no slots available" message
                            setErrorModal(true);
                            setresponseErrorMsg(
                              'No slots are available for this physician. Please select a different physician or appointment type.',
                            );

                            // Disable the physician dropdown when no slots are available
                            set_PhyName(true);
                            set_AptDate(true);
                            set_AptSlot(true);
                          }
                        }
                      } catch (error) {
                        setAvailabilityDate([]);
                      }
                    }}
                    valuePicked={details.doctor_name}
                    DropIcon={physicianLoader ? false : true}
                    // Disable the dropdown until appointment type is selected and auto-population is complete
                    disabled={phy_name || !details.appointment_purpose}
                  />
                  {physicianLoader && details.appointment_purpose && (
                    <ActivityIndicator
                      style={{paddingHorizontal: 10}}
                      color={theme.colors.brandPrimary}
                      size="large"
                    />
                  )}
                </View>
              </View>
            </View>
            <View style={{height: 5}} />
            <View style={{height: 5}} />

            {/* Diagonisis */}
            <View style={{width: '100%', flexDirection: 'column'}}>
              <View
                style={[
                  details.doctor_name === '' || appointmentType === ''
                    ? s.parentDisabled
                    : s.parentEnabled,
                  {width: '100%'},
                ]}>
                <OurTextInput
                  value={''}
                  placeholder={''}
                  editable={false}
                  title="Diagnosis"
                  bgColor={theme.colors.brandBackground}
                  customHeight={80}
                  paddingCustomLeft={0}
                  paddingCustomRight={0}
                  borderRadius={8}
                  onPress={() => {}}
                  validationOk={true}
                />
                <View style={s?.dataPicker}>
                  <DataPicker
                    items={getDiagnosisItems(details.doctor_name)}
                    title={'Select Diagnosis'}
                    placeholder={
                      diagnosisList.length === 0 && details.doctor_name
                        ? 'Select Diagnosis'
                        : 'Select Diagnosis'
                    }
                    onValueChange={value => {
                      setDetails({
                        ...details,
                        diagnosis: value,
                      });
                    }}
                    valuePicked={details.diagnosis}
                    DropIcon={true}
                    disabled={
                      !details.doctor_name || details.doctor_name === ''
                        ? true
                        : false
                    }
                  />
                </View>
              </View>
            </View>
            <View style={{height: 5}} />
            <View style={{height: 5}} />
            <View style={{width: '100%', flexDirection: 'column'}}>
              {/* appointDate  */}
              <View
                style={[
                  !details.doctor_name || details.doctor_name === ''
                    ? s.parentDisabled
                    : s.parentEnabled,
                  {width: '100%'},
                ]}>
                <OurTextInput
                  value={''}
                  placeholder={''}
                  editable={false}
                  title="Date"
                  bgColor={theme.colors.brandBackground}
                  customHeight={80}
                  paddingCustomLeft={0}
                  paddingCustomRight={0}
                  onPress={() => {}}
                  validationOk={true}
                />
                <View style={s?.dataPicker}>
                  <DataPicker
                    items={availabilityDate}
                    title={'Appointment Date'}
                    placeholder={
                      dateLoader || changingPhysician
                        ? 'Loading appointment dates...'
                        : availabilityDate.length === 0
                          ? 'No dates available'
                          : 'Appointment Date'
                    }
                    onValueChange={async value => {
                      // Show loading indicator for time slots
                      setTimeSlotLoader(true);

                      // handleInputs('appointment_date', value);
                      setDetails({
                        ...details,
                        appointment_date: value,
                        appointment_time: '',
                      });

                      setApptDate(value);

                      try {
                        // Get time slots for this date
                        if (openSlots && details.doctor_name) {
                          // Find the provider in the openSlots data
                          const provider = details.doctor_name.trim();

                          // First try exact match
                          let providerData = openSlots[provider];

                          // If no exact match, try to find a close match for the provider
                          if (!providerData) {
                            const providerKeys = Object.keys(openSlots);
                            const matchedProvider = providerKeys.find(
                              key =>
                                key
                                  .toLowerCase()
                                  .includes(provider.toLowerCase()) ||
                                provider
                                  .toLowerCase()
                                  .includes(key.toLowerCase()),
                            );

                            if (matchedProvider) {
                              providerData = openSlots[matchedProvider];
                            }
                          }

                          if (providerData && providerData[value]) {
                            // Get the time slots for this date
                            const slots = providerData[value];

                            // Format the time slots for the dropdown
                            const formattedSlots = slots.map(slot => ({
                              label: convertTo12HourFormat(slot.split('&')[0]),
                              value: slot,
                            }));

                            // Set the time slots
                            setavailableTimeSlot(formattedSlots);
                          } else {
                            setavailableTimeSlot([]);

                            // If we have a physician ID, try to fetch slots directly
                            const physician = phyList.find(
                              p =>
                                p.value.toLowerCase().trim() ===
                                provider.toLowerCase().trim(),
                            );

                            if (
                              physician &&
                              physician.id &&
                              details.appointment_purpose
                            ) {
                              try {
                                const res = await axiosPrivate.get(
                                  `${config[config.STAGING].PATIENT_INFO}${preferredPrac}/booking-slots?location_id=${locationId}&patient_type=${pat_type}&appt_type_id=${details.appointment_purpose}&provider_id=${physician.id}`,
                                  {
                                    indication: 'Newappointment',
                                  },
                                );

                                if (
                                  res.data &&
                                  Array.isArray(res.data) &&
                                  res.data.length > 0
                                ) {
                                  // Organize the slots
                                  const newSlotData = organizeAppointments(
                                    res.data,
                                  );

                                  // Update the openSlots state
                                  setOpenSlots(prevSlots => {
                                    // Log the current keys in openSlots

                                    // Log the new keys being added

                                    return {
                                      ...prevSlots,
                                      ...newSlotData,
                                    };
                                  });

                                  // Try to get slots for the selected date
                                  const providerName =
                                    Object.keys(newSlotData)[0];
                                  if (
                                    providerName &&
                                    newSlotData[providerName] &&
                                    newSlotData[providerName][value]
                                  ) {
                                    const newSlots =
                                      newSlotData[providerName][value];

                                    // Format the time slots for the dropdown
                                    const formattedSlots = newSlots.map(
                                      slot => ({
                                        label: convertTo12HourFormat(
                                          slot.split('&')[0],
                                        ),
                                        value: slot,
                                      }),
                                    );

                                    // Set the time slots
                                    setavailableTimeSlot(formattedSlots);
                                  }
                                }
                              } catch (fetchError) {}
                            }
                          }
                        }
                      } catch (error) {
                        setavailableTimeSlot([]);
                      } finally {
                        // Hide loading indicator
                        setTimeSlotLoader(false);
                      }
                    }}
                    valuePicked={
                      details.appointment_date && ApptFormatDate(apptDate)
                    }
                    DropIcon={dateLoader ? false : true}
                    disabled={
                      !details.doctor_name ||
                      details.doctor_name === '' ||
                      dateLoader ||
                      changingPhysician ||
                      availabilityDate.length === 0
                        ? true
                        : false
                    }
                  />
                  {(dateLoader || changingPhysician) && (
                    <ActivityIndicator
                      style={{paddingHorizontal: 10}}
                      color={theme.colors.brandPrimary}
                    />
                  )}
                </View>
              </View>
              {/* appointSlots */}
              <View
                style={[
                  !details.appointment_date || details.appointment_date === ''
                    ? s.parentDisabled
                    : s.parentEnabled,
                  {width: '100%'},
                ]}>
                {/* <View style={{ height: 5 }} /> */}
                <OurTextInput
                  value={''}
                  placeholder={''}
                  editable={false}
                  title="Time "
                  bgColor={theme.colors.brandBackground}
                  customHeight={80}
                  paddingCustomLeft={0}
                  paddingCustomRight={0}
                  onPress={() => {}}
                  validationOk={true}
                />
                <View style={s?.dataPicker}>
                  <DataPicker
                    items={availableTimeSlot}
                    title={'Appointment Slot'}
                    placeholder={
                      timeSlotLoader
                        ? 'Loading time slots...'
                        : 'Appointment Slot'
                    }
                    onValueChange={value => {
                      setDetails({
                        ...details,
                        appointment_time: value,
                        appointmentid: value.split('&')[1],
                      });
                      set_AptSlot(false);
                    }}
                    valuePicked={
                      details.appointment_time
                        ? convertTo12HourFormat(
                            details.appointment_time.split('&')[0],
                          )
                        : null
                    }
                    style={{color: 'black'}}
                    DropIcon={true}
                    disabled={
                      !details.appointment_date ||
                      details.appointment_date === '' ||
                      timeSlotLoader
                        ? true
                        : false
                    }
                  />
                  {timeSlotLoader && (
                    <ActivityIndicator
                      style={{paddingHorizontal: 10}}
                      color={theme.colors.brandPrimary}
                    />
                  )}
                </View>
              </View>
            </View>
            <View style={{height: 5}} />
            <View style={{height: 5}} />
            {/* appointment Reason */}
            <View style={{width: '100%'}}>
              <OurTextInput
                value={details.note}
                placeholder={'add notes (optional).'}
                // editable={false}
                title="Notes"
                bgColor={theme.colors.brandBackground}
                customHeight={80}
                paddingCustomLeft={0}
                paddingCustomRight={0}
                // onPress={() => {}}
                onChangeText={text =>
                  setDetails(prev => {
                    return {
                      ...prev,
                      note: text,
                    };
                  })
                }
                validationOk={true}
              />
            </View>
            <View style={{height: 60, width: 2}} />
          </ScrollView>
        </KeyboardAwareScrollView>
        {!keyboardOpened && (
          <View style={s?.button}>
            <Button
              text={btnLoader ? <ActivityIndicator color="#FFFFFF" /> : 'Save'}
              shadow
              color={theme.colors.white}
              backgroundColor={theme.colors.brandPrimary}
              onPress={() => HandleCreateAppt()}
              disabled={validationLast()}
            />
          </View>
        )}
        {/* Response handler  */}
        <SuccessMsgHandler
          HandleModal={requestModal}
          setHandleModal={() => {
            setDetails({
              appointment_date: '',
              appointment_time: '',
              doctor_name: '',
              appointment_location: '',
              appointment_purpose: '',
              note: '',
              locationId: '',
              appointmentid: '',
            });
            setRequestModal(false);
          }}
          msg={
            'We are currently processing your appointment.Thank you for your patience.'
          }
          clickedOk={() => navigation.goBack()}
        />
        <ErrorMsgHandler
          HandleModal={errorModal}
          setHandleModal={setErrorModal}
          msg={responseErrorMsg}
          clickedOk={() => {}}
        />
        <DefaultModal
          onTopButtonPress={
            noSlotsMessage
              ? () => {
                  // When OK is clicked, just close the modal
                  setAppPopup(false);
                  setNoSlotsMessage('');
                }
              : () => {
                  // When the user clicks "Select Physician", enable the physician dropdown

                  set_PhyName(false); // Enable the dropdown
                  setAppPopup(false); // Close the popup

                  // Clear any auto-selected physician
                  setDetails(prevDetails => ({
                    ...prevDetails,
                    doctor_name: '', // Clear the doctor name to prevent auto-selection
                    appointment_date: '',
                    appointment_time: '',
                    diagnosis: '',
                  }));

                  // Don't add an empty placeholder - just use the physician list as is
                  // Force a small delay to ensure the dropdown is reset
                  setTimeout(() => {
                    setDetails(prevDetails => ({
                      ...prevDetails,
                      doctor_name: '', // Ensure doctor_name is empty
                    }));
                  }, 100);
                }
          }
          onTopButtonText={noSlotsMessage ? 'OK' : 'Select Physician'}
          onBottomButtonPress={
            noSlotsMessage ? handleDoneRequest : handleDoneRequest
          }
          onBottomButtonText={
            noSlotsMessage ? 'Call for Requesting' : 'Call for Requesting'
          }
          text={
            noSlotsMessage
              ? noSlotsMessage
              : autoPopulateBlockType.includes(details.appointment_purpose)
                ? 'For this appointment type, you need to select a physician manually.'
                : lastAppointmentData &&
                    lastAppointmentData.shouldBlockAutoPopulate
                  ? 'Auto-selection is disabled for this appointment type. Please select a physician manually.'
                  : availabilityDate.length === 0 && details.doctor_name
                    ? 'No slots are available for this physician. Please select a different physician or appointment type.'
                    : 'No matching physician found from your last appointment. Please select a physician manually or call for assistance.'
          }
          modalVisible={appPopup}
          setModalVisible={item => {
            // Only close the popup
            setAppPopup(item);

            // Only enable the dropdown if there are slots available (no noSlotsMessage)
            // If there are no slots, keep the dropdown disabled
            if (!noSlotsMessage) {
              set_PhyName(false);
            }

            // Clear the noSlotsMessage when closing the modal
            if (!item) {
              setNoSlotsMessage('');
            }
          }}
        />
      </View>
    </>
  );
};

const createStyle = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.brandBackground,
    },

    dataPicker: {
      position: 'absolute',
      bottom: Platform.OS === 'ios' ? 20 : 8,
      left: Platform.OS === 'ios' ? 20 : 6,
      flexDirection: 'row',
      width: '100%',
      // backgroundColor:'black'
    },
    componentContainer: {
      width: '100%',
      marginTop: 30,
    },
    block: {
      justifyContent: 'space-between',
      alignItems: 'center',
      flex: 1,
    },
    block2: {
      width: '100%',
    },

    button: {
      paddingHorizontal: theme.metrics.x4,
      marginBottom: 20,
      // marginTop: 20,
    },
    parentDisabled: {
      opacity: 0.5,
    },
    parentEnabled: {
      opacity: 1,
    },
  });

export default NewAppointment;
