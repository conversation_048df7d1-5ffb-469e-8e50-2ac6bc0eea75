import moment from 'moment';
import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { ConvertDateToStr, formatTime } from '../../helpers/convertDateToStr';
import GetAppointmentList from '../../hooks/getAppointmentList';
import { AppointmentResult } from '../ui/components/AppointmentResult';
import AppointmentSwitch from '../ui/components/AppointmentSwitch/AppointmentSwitch';
import { Button } from '../ui/components/Button';
import { Header } from '../ui/components/Header';
import { MyAccount } from '../ui/components/MyAccount';
import { TextComponent } from '../ui/components/TextComponent';
import { Theme, useThemeContext } from '../ui/theme';
import { theme } from '../ui/theme/default/theme';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>g<PERSON><PERSON><PERSON>,
  NewAppointmentHand<PERSON>
} from '../ui/components/MessageHandler/messageHandler';
import GetPracticeInfo from '../../helpers/getPracticeInfo';
import GetPhyImgById from '../../helpers/getPhysicianImgById';
import { Text } from 'react-native-svg';
import { color } from 'react-native-reanimated';
import AsyncStorage from '@react-native-async-storage/async-storage';
import GetRsiSenMapping from '../../hooks/getRsisegmapping';
import GetRestrictNewPatient from '../../hooks/getRestrictNewPatient';
import PlainLoader from '../Plainloader/PlainLoader';


const AppointmentScreen = ({ navigation, route }) => {
  const { selectView } = route.params;
  const dob = useSelector(state => state.userInfo?.userInfo?.dob);
  const { s } = useThemeContext(createStyle);
  const [selected, setSelected] = useState(selectView);
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setresponseErrorMsg] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [appPopup, setAppPopup] = useState(false);
  const [agelimitmsg, setAgelimitmessage] = useState('');
  const [apptlock, setApptLock] = useState(false);
  const defaultDoctor =
    'data:image/png;base64,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';
  const upcomingAppointments = useSelector(
    state => state.AppointmentDetails.upcomingAppointments
  );

  const previousAppointments = useSelector(
    state => state.AppointmentDetails.previousAppointments
  );

  const fetchingAPI = useSelector(
    state => state.AppointmentDetails.fetchingAPI,
  );

  //rsi-seg mapping data
  const rsi_segAPI = useSelector(
    state => state.RsiSenMappingDetails.rsiSenMappingData,
  );

  // restrict new patient data with null safety
  const restrictNewPatientData = useSelector(
    state => state.RestrictNewPatient || { fetchingAPI: false, allowed: true, error: null },
  );

  console.log("restrictNewPatientData",restrictNewPatientData);

  // physicians list redux variable
  const physiciansList = useSelector(state => state.PhysicianList.data);

  // Check if restrict new patient API is still loading
  const isRestrictNewPatientLoading = restrictNewPatientData?.fetchingAPI || false;

  const currentAppointmentType = '';
  const handleSelect = item => {
    if (item !== selected) {
      setSelected(item);
    }
  };
  const dispatch = useDispatch();

  const gestureconfig = {
    velocityThreshold: 0.3,
    directionalOffsetThreshold: 80,
  };

  const handleEmergencyCall = async () => {
    try {
      const practice = await GetPracticeInfo();
      let number = '';
      if (Platform.OS === 'ios') {
        if (practice.preferred_practice === 'rsi') {
          number = 'telprompt:+1**********';
        } else if (practice.preferred_practice === 'sen') {
          number = 'telprompt:+1**********';
        }
      } else {
        if (practice.preferred_practice === 'rsi') {
          number = `tel:**********`;
        } else if (practice.preferred_practice === 'sen') {
          number = `tel:**********`;
        }
      }
      Linking.openURL(number);
    } catch (e) {
      console.log('error in linking: ', e);
    }
  };

  useEffect(() => {
    console.log('AppointmentScreen useEffect - fetchingAPI:', fetchingAPI);

    if (currentAppointmentType) {
      setSelected(currentAppointmentType);
      handleSelect(currentAppointmentType);
    }

    if (fetchingAPI) {
      console.log('fetchingAPI is true, making API calls...');
      (async () => {
        const practiceInfo = await GetPracticeInfo();
        console.log('practiceInfo:', practiceInfo);

        GetAppointmentList(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practiceInfo.preferred_practice,
        );
        GetRsiSenMapping(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practiceInfo.preferred_practice,
        );
        console.log('Calling GetRestrictNewPatient with practice:', practiceInfo.preferred_practice);
        GetRestrictNewPatient(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practiceInfo.preferred_practice,
        );
      })();
    } else {
      console.log('fetchingAPI is false, not making API calls');
    }
  }, []);

  useEffect(() => {
    setSelected(selectView);
  }, [selectView]);

  // Always call GetRestrictNewPatient on component mount
  useEffect(() => {
    (async () => {
      try {
        const practiceInfo = await GetPracticeInfo();
        console.log('Calling GetRestrictNewPatient on mount with practice:', practiceInfo.preferred_practice);
        GetRestrictNewPatient(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practiceInfo.preferred_practice,
        );
      } catch (error) {
        console.error('Error in GetRestrictNewPatient mount useEffect:', error);
      }
    })();
  }, []);



  const handleBack = () => {
    // console.log('navigation is: ', navigation, route)
    // const parent = navigation.getParent();
    // const state = parent.getState();
    // console.log('state is: ', state);
    navigation.goBack();
  };

  const check_age_limit = async () => {
    const age = moment().diff(moment(dob, "YYYY-MM-DD"), 'years');
    const minAgeLimit = rsi_segAPI?.mapping_data?.min_age_limit;
    if (minAgeLimit >= age) {
      console.log("popup");
      setAppPopup(true)
    } else {
      handleRequestNewAppt();
    }
  };



  const handleRequestNewAppt = async () => {
    const checkTime = await AsyncStorage.getItem('newAppt');
    if (checkTime) {
      const time = new Date(checkTime).getTime();
      const currentTime = new Date().getTime();
      if ((currentTime - time) / (1000 * 60) > 1) {
        navigation.navigate('NewAppointment');
      } else {
        setApptLock(true);
      }
    } else {
      navigation.navigate('NewAppointment');
    }
  };

  // Show PlainLoader if restrict new patient API is still loading
  if (isRestrictNewPatientLoading) {
    return <PlainLoader />;
  }

  return (
    <View style={s?.container}>
      <Header
        // spaceLeft
        backgroundColor
        title="My appointments"
        spaceRight
        onBackPress={() => handleBack()}
      // onBellPress={true}
      />

      <View style={{ marginTop: theme.metrics.x2 }}>

        <AppointmentSwitch
          selected={selected}
          setSelected={handleSelect}
          comingCount={[
            ...(upcomingAppointments || []),
            ...(previousAppointments || [])
          ].filter(
            data => moment(data.appt_date || data.appt_date, 'YYYY-MM-DD').isSameOrAfter(moment(), 'day')
          ).length}
          resultCount={[
            ...(upcomingAppointments || []),
            ...(previousAppointments || [])
          ].filter(
            data => moment(data.appt_date || data.appt_date, 'YYYY-MM-DD').isBefore(moment(), 'day')
          ).length}
        />

      </View>
      {!fetchingAPI ? (
        <ScrollView
          bounces={false}
          style={{ flex: 1 }}
          contentContainerStyle={s?.appoinymentsBlock}>
          {
            selected === 1 &&
              upcomingAppointments && upcomingAppointments.length !== 0 ? (
              upcomingAppointments.filter(
                data =>
                  moment(data.appt_date).format('YYYY-MM-DD') >=
                  moment().format('YYYY-MM-DD'),
              ).length ? (
                upcomingAppointments.filter(
                  data =>
                    moment(data.appt_date).format('YYYY-MM-DD') >=
                    moment().format('YYYY-MM-DD'),
                )
                  .sort(
                    (a, b) =>
                      moment(`${a.appt_date} ${a.appt_time}`) -
                      moment(`${b.appt_date} ${b.appt_time}`),
                  )
                  .map(items => (
                    <MyAccount
                      onPress={() => {
                        navigation.navigate('AppointmentDetails', {
                          appointment: items,
                          selected: selected,
                        });
                      }}
                      doctor={
                        items.dw_physician_id
                          ? items.physician_fname && items.physician_lname
                            ? 'Dr. ' +
                            items.physician_fname +
                            ' ' +
                            items.physician_lname
                            : 'Physician not assigned'
                          : 'Physician not assigned'
                      }
                      appointmentType={ConvertDateToStr(items.appt_date)}
                      time={formatTime(items.appt_time)}
                      image={
                        GetPhyImgById(items.dw_physician_id, physiciansList)
                          ? GetPhyImgById(items.dw_physician_id, physiciansList)
                          : defaultDoctor
                      }
                      date={
                        moment(items.appt_date).format('DD.MM') ===
                        moment().format('DD.MM') && 'Today'
                      }
                      type={1}
                      warning={false}
                    />
                  ))
              ) : (
                <View
                  style={{
                    height: '100%',
                    alignItems: 'center',
                    marginTop: '50%',
                  }}>
                  <TextComponent headerText={'No upcoming appointments yet'} />
                </View>
              )
            ) :
              (
                <>
                  {selected === 1 && (
                    <View
                      style={{
                        height: '100%',
                        alignItems: 'center',
                        marginTop: theme.metrics.height * 0.6,
                      }}>
                      <TextComponent headerText={'No upcoming appointments yet'} />
                    </View>
                  )}
                </>
              )}
          {
            selected === 2 &&
              previousAppointments &&
              previousAppointments.length !== 0 ?
              (
                previousAppointments.filter(
                  data =>
                    moment(data.date).format('YYYY-MM-DD') >=
                    moment().format('YYYY-MM-DD'),
                ).length ? (
                  previousAppointments.filter(data => moment(data.appt_date, 'YYYY-MM-DD').isBefore(moment()))
                    .sort((a, b) =>
                      moment(`${b.appt_date} ${b.appt_time}`, 'YYYY-MM-DD HH:mm:ss') -
                      moment(`${a.appt_date} ${a.appt_time}`, 'YYYY-MM-DD HH:mm:ss')
                    )
                    .map(items => (
                      <AppointmentResult
                        onPress={() => {
                          navigation.navigate('AppointmentResultDetails', {
                            appointment: items,
                            selected: selected,
                          });
                        }}
                        image={
                          GetPhyImgById(items.dw_physician_id, physiciansList)
                            ? GetPhyImgById(items.dw_physician_id, physiciansList)
                            : defaultDoctor
                        }
                        doctor={
                          items.dw_physician_id
                            ? items.physician_fname && items.physician_lname
                              ? 'Dr. ' +
                              items.physician_fname +
                              ' ' +
                              items.physician_lname
                              : 'Physician not assigned'
                            : 'Physician not assigned'
                        }
                        date={ConvertDateToStr(items.appt_date)}
                        payed={items.patient_claim_status === 'CLOSED' ? true : false}
                        outstandingAmount={items.patient_outstanding}
                      />
                    ))
                ) : (
                  <View
                    style={{
                      height: '100%',
                      alignItems: 'center',
                      marginTop: '50%',
                    }}>
                    <TextComponent headerText={'No upcoming appointments yet'} />
                  </View>
                )
              ) : previousAppointments && previousAppointments.length !== 0 ? (
                previousAppointments.filter(
                  data =>
                    moment(data.date).format('YYYY-MM-DD') <
                    moment().format('YYYY-MM-DD'),
                ).map(items => (
                  <AppointmentResult
                    onPress={() => {
                      navigation.navigate('AppointmentResultDetails', {
                        appointment: items,
                        selected: selected,
                      });
                    }}
                    image={
                      GetPhyImgById(items.dw_physician_id, physiciansList)
                        ? GetPhyImgById(items.dw_physician_id, physiciansList)
                        : defaultDoctor
                    }
                    doctor={
                      items.dw_physician_id
                        ? items.physician_fname && items.physician_lname
                          ? 'Dr. ' +
                          items.physician_fname +
                          ' ' +
                          items.physician_lname
                          : 'Physician not assigned'
                        : 'Physician not assigned'
                    }
                    date={ConvertDateToStr(items.appt_date)}
                    payed={items.patient_claim_status === 'CLOSED' ? true : false}
                    outstandingAmount={items.patient_outstanding}
                  />
                ))
              ) :
                (
                  <>
                    {selected === 2 && (
                      <View
                        style={{
                          height: '100%',
                          alignItems: 'center',
                          marginTop: theme.metrics.height * 0.6,
                        }}>
                        <TextComponent headerText={'No appointments results yet'} />
                      </View>
                    )}
                  </>
                )}
        </ScrollView>
      ) : (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
        </View>
      )}
      <View style={s?.button}>
        <Button
          text="Call clinic for urgent assistance"
          // shadow
          color={theme.colors.watermelon}
          border={{ borderWidth: 1.5, borderColor: theme.colors.watermelon }}
          backgroundColor="transparent"
          // backgroundColor={theme.colors.brandPrimary}
          onPress={handleEmergencyCall}
        />
        <Button
          text="Request New Appointment"
          shadow
          color="#fff"
          backgroundColor={restrictNewPatientData?.allowed ? theme.colors.brandPrimary : theme.colors.gray}
          disabled={!restrictNewPatientData?.allowed}
          // onPress={() => navigation.navigate('NewAppointment')}
          onPress={() => check_age_limit()}
        />
      </View>
      {/* Response handler  */}
      <SuccessMsgHandler
        HandleModal={success}
        setHandleModal={setSuccess}
        msg={successMessage}
        clickedOk={() => { }}
      />
      <SuccessMsgHandler
        HandleModal={apptlock}
        setHandleModal={setApptLock}
        msg={
          'We are currently processing your appointment.Thank you for your patience.'
        }
        clickedOk={() => { }}
      />
    <NewAppointmentHandler
       HandleModal={appPopup}
         setHandleModal={setAppPopup}
        msg={
          'We are currently processing your appointment.Thank you for your patience.'
        }
        clickedOk={() => { }}
      />
      <ErrorMsgHandler
        HandleModal={errorModal}
        setHandleModal={setErrorModal}
        msg={responseErrorMsg}
        clickedOk={() => { }}
      />
    </View>
  );
};

const createStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.brandBackground,
    },
    appoinymentsBlock: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    button: {
      paddingHorizontal: theme.metrics.x4,
      marginBottom: theme.metrics.x5,
    },
  });

export default AppointmentScreen;
