import React, {useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {Feedback} from '../../ui/components/Feedback';
import {Header} from '../../ui/components/Header';
import {Warning} from '../../ui/components/Warning';
import {useThemeContext} from '../../ui/theme';
import {theme} from '../../ui/theme/default/theme';
import {Communication} from '../../ui/components/Communication';
import {useDebounce} from '../../../helpers/debounce';
import {ConvertDateToStrWithTime} from '../../../helpers/convertDateToStr';
import getMedByAppt from '../../../hooks/getMedicationByAppt';
import {useSelector} from 'react-redux';
import GetPracticeInfo from '../../../helpers/getPracticeInfo';
// import getDiagnosisByAppt from '../../../hooks/getDiagnosisByAppt';
// import CustomText from '../../ui/components/customText/customText';
import GetPhyImgById from '../../../helpers/getPhysicianImgById';
import GetPhyRatingById from '../../../helpers/getPhysicianRatingById';
// import MoreLessText from '../../ui/components/MoreLessText';
import GetPhyReviewNumById from '../../../helpers/getPhyReviewNumById';
import {PDFViewer} from '../../ui/components/PDFViewer';
import {Button} from '../../ui/components/Button';
import {getPresignedUrl} from '../../../helpers/getPresignedUrl';
import CustomText from '../../ui/components/customText/customText';

const AppointmentResultDetails = ({navigation, route}) => {
  const {appointment} = route.params;
  console.log('appointmentResultDetailsScreen', appointment);
  const {s} = useThemeContext(createStyle);
  const warning = false;
  const {debounce} = useDebounce();
  const [medicationList, setMedicationList] = useState({
    fetching: false,
    data: {},
  });
  const defaultDoctor =
    'data:image/png;base64,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';

  // const [diagnosisText, setDiagnosisText] = useState({
  //   fetching: false,
  //   encountertype: null,
  //   primary_diagnosis: [],
  //   secondary_diagnosis: [],
  // });

  // const [discussion, setDiscussion] = useState({
  //   fetching: false,
  //   content: [],
  //   // showmore: false,
  // });
  const [showReportPdf, setShowReportPdf] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [fileUrl, setFileUrl] = useState({
    isFetching: true,
    url: null,
  });
  const [imageUrl, setImageUrl] = useState({
    isFetching: true,
    url: null,
  });

  // physicians list redux variable
  const physiciansList = useSelector(state => state.PhysicianList.data);
  const {userInfo} = useSelector(state => state);

  useEffect(() => {
    (async () => {
      const practiceInfo = await GetPracticeInfo();

      // Fetch file pre-signed URL using utility function
      if (fileUrl.isFetching && appointment.filename) {
        try {
          console.log(
            'Fetching Visit Summary pre-signed URL for:',
            appointment.filename,
          );
          const response = await getPresignedUrl(
            'get',
            practiceInfo.preferred_practice,
            'patientsummary',
            userInfo.userInfo.chart_number,
            appointment.filename,
          );
          console.log('Visit Summary pre-signed URL response:', response.data);
          setFileUrl({
            isFetching: false,
            url: response.data.url || '',
          });
        } catch (error) {
          console.error('Error fetching file pre-signed URL:', error);
          console.error('Error details:', error.response?.data);
          setFileUrl({
            isFetching: false,
            url: '',
          });
        }
      } else if (!appointment.filename) {
        setFileUrl({
          isFetching: false,
          url: null,
        });
      }

      // Fetch image URL if image_filename exists using utility function
      if (imageUrl.isFetching && appointment.image_filename) {
        try {
          console.log(
            'Fetching Image pre-signed URL for:',
            appointment.image_filename,
          );
          const response = await getPresignedUrl(
            'get',
            practiceInfo.preferred_practice,
            'encounterimages',
            userInfo.userInfo.chart_number,
            appointment.image_filename,
          );
          console.log('Image pre-signed URL response:', response.data);
          setImageUrl({
            isFetching: false,
            url: response.data.url || '',
          });
        } catch (error) {
          console.error('Error fetching image pre-signed URL:', error);
          console.error('Error details:', error.response?.data);
          setImageUrl({
            isFetching: false,
            url: '',
          });
        }
      } else if (!appointment.image_filename) {
        setImageUrl({
          isFetching: false,
          url: null,
        });
      }

      setMedicationList({...medicationList, fetching: true});
      const medicationListResponse = await getMedByAppt({
        appt_id: appointment.appointmentid,
        practice: practiceInfo.preferred_practice,
      });

      setMedicationList({...medicationList, fetching: false});

      if (medicationListResponse.status === 200) {
        setMedicationList({
          ...data,
          sys_med: medicationListResponse.sys_med,
          ocular_med: medicationListResponse.ocular_med,
        });
      }
    })();
  }, []);
  // Temp variable
  const getAppointmentsByIdFetching = '';
  const cancelAppointmentFetching = '';
  const medicineDetails = [
    {
      encounter_id: '830043',
      name: 'Atorvastatin (As Atorvastatin Calcium) 10 Mg Oral Tablet',
      physician_fname: 'Maggie',
      physician_lname: 'Shuler',
      medication_text: null,
      start_date: '2023-02-02',
      stop_date: null,
      dose: null,
      doseunit: 'Unknown',
    },
    {
      encounter_id: '830043',
      name: 'Cellcept 500mg',
      physician_fname: 'Maggie',
      physician_lname: 'Shuler',
      medication_text: null,
      start_date: '2023-02-02',
      stop_date: null,
      dose: null,
      doseunit: null,
    },
  ];

  const HandleSetShowReportPdf = status => {
    setShowReportPdf(() => status);
  };

  const HandleSetShowImageViewer = status => {
    setShowImageViewer(() => status);
  };

  const data = {
    messages: [
      {
        content: fileUrl.isFetching ? (
          <ActivityIndicator color={theme.colors.brandPrimary} />
        ) : (
          <View style={{flex: 1, marginVertical: 5}}>
            {console.log('appointmentFileCheck: ', appointment.filename)}
            {console.log('appointmentImageCheck: ', appointment.image_filename)}
            {appointment.filename && (
              <PDFViewer
                fileName={appointment.filename}
                fileUrl={fileUrl.url}
                showPdf={showReportPdf}
                setShowPdf={HandleSetShowReportPdf}
              />
            )}
            {appointment.image_filename && (
              <PDFViewer
                fileName={appointment.image_filename}
                fileUrl={imageUrl.url}
                showPdf={showImageViewer}
                setShowPdf={HandleSetShowImageViewer}
              />
            )}
            <View style={s?.contactBlock}>
              <Button
                onPress={() => {
                  HandleSetShowReportPdf(true);
                }}
                customPadding={theme.metrics.x3}
                text={
                  appointment.filename ? 'Visit Summary' : 'No Visit Summary'
                }
                fontWeight="600"
                color={
                  appointment.filename
                    ? theme.colors.brandPrimary
                    : theme.colors.gray
                }
                border={{
                  borderWidth: 1.5,
                  borderColor: appointment.filename
                    ? theme.colors.brandPrimary
                    : theme.colors.gray,
                }}
                disabled={!appointment.filename}
                backgroundColor="transparent"
              />

              <View style={{marginTop: theme.metrics.x2}}>
                <Button
                  onPress={() => {
                    HandleSetShowImageViewer(true);
                  }}
                  customPadding={theme.metrics.x3}
                  text={appointment.image_filename ? 'Images' : 'No Images'}
                  fontWeight="600"
                  color={
                    appointment.image_filename
                      ? theme.colors.brandPrimary
                      : theme.colors.gray
                  }
                  border={{
                    borderWidth: 1.5,
                    borderColor: appointment.image_filename
                      ? theme.colors.brandPrimary
                      : theme.colors.gray,
                  }}
                  disabled={!appointment.image_filename}
                  backgroundColor="transparent"
                />
              </View>
            </View>
            {/* <CustomText style={{ paddingVertical: 5 }}>
              <CustomText
                style={{
                  ...theme.fonts.texts.bodySmallSemibold,
                  color: theme.colors.mainGray,
                }}>
                Primary Diagnosis:{' \n'}
              </CustomText>

              {diagnosisText?.primary_diagnosis?.length > 0
                ? diagnosisText?.primary_diagnosis.map((item, index) => (
                  <CustomText
                    style={{
                      ...theme.fonts.texts.bodySmallRegular,
                      color: theme.colors.darkGrey,
                    }}>
                    {item}
                    {index < diagnosisText?.primary_diagnosis.length - 1 &&
                      '. \n'}
                  </CustomText>
                ))
                : 'NA'}
            </CustomText>
            <CustomText style={{ paddingVertical: 5 }}>
              <CustomText
                style={{
                  ...theme.fonts.texts.bodySmallSemibold,
                  color: theme.colors.mainGray,
                }}>
                Secondary Diagnosis:{' \n'}
              </CustomText>{' '}
              {diagnosisText?.secondary_diagnosis?.length > 0
                ? diagnosisText?.secondary_diagnosis.map((item, index) => (
                  <CustomText
                    style={{
                      ...theme.fonts.texts.bodySmallRegular,
                      color: theme.colors.darkGrey,
                    }}>
                    {item}
                    {index < diagnosisText?.secondary_diagnosis.length - 1 &&
                      '. \n'}
                  </CustomText>
                ))
                : 'NA'}
            </CustomText> */}
          </View>
        ),
      },
    ],
    // disscussion: [
    //   {
    //     content: discussion.fetching ? (
    //       <ActivityIndicator color={theme.colors.brandPrimary} />
    //     ) : (
    //       <View style={{ flex: 1, marginVertical: 5 }}>
    //         <MoreLessText style={{ paddingVertical: 5 }} numberOfLines={5}>
    //           <CustomText>
    //             <CustomText
    //               style={{
    //                 ...theme.fonts.texts.bodySmallSemibold,
    //                 color: theme.colors.mainGray,
    //               }}>
    //               Discussion:{' \n'}
    //             </CustomText>
    //             {discussion?.content?.length > 0
    //               ? discussion?.content.map((item, index) => (
    //                 <CustomText
    //                   style={{
    //                     ...theme.fonts.texts.bodySmallRegular,
    //                     color: theme.colors.darkGrey,
    //                   }}>
    //                   {item}
    //                   {index < discussion?.content.length - 1 && '. \n'}{''}
    //                 </CustomText>
    //               ))
    //               : 'NA'}
    //           </CustomText>
    //         </MoreLessText>
    //       </View>
    //     ),
    //   },
    // ],
  };
  const hideItems = () => {
    setShowImages(false);
    setShowAudio(false);
    setShowVideo(false);
  };

  const [showImages, setShowImages] = useState(true);
  const [showAudio, setShowAudio] = useState(true);
  const [showVideo, setShowVideo] = useState(true);

  return (
    <SafeAreaView style={s?.container}>
      <Header
        onBackPress={() => navigation.goBack()}
        backgroundColor
        title="Visit Details"
        // onChatPress={() => {
        //   debounce(() => {
        //     hideItems();
        //     navigation.navigate('ChatListScreen');
        //   });
        // }}
        spaceRight
      />
      {!getAppointmentsByIdFetching && !cancelAppointmentFetching ? (
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={s?.appoinymentBlock}>
          {appointment.appt_date && (
            <View style={{flex: 1, alignItems: 'center'}}>
              <View style={s?.block}>
                {/* section to show warning */}
                {warning && (
                  <View style={s?.itemsSeperator}>
                    <Warning type={1} time={'8 pm'} />
                  </View>
                )}
                <View style={s?.itemsSeperator}>
                  <Feedback
                    image={
                      GetPhyImgById(appointment.dw_physician_id, physiciansList)
                        ? GetPhyImgById(
                            appointment.dw_physician_id,
                            physiciansList,
                          )
                        : defaultDoctor
                    }
                    doctor={
                      appointment.dw_physician_id
                        ? 'Dr. ' +
                          appointment.physician_fname +
                          ' ' +
                          appointment.physician_lname
                        : 'Physician not assigned'
                    }
                    type={1}
                    // onPress={() => {
                    //   navigation.navigate('ChatListScreen');
                    // }}
                    // setActive={true}
                    reviewsNumber={GetPhyReviewNumById(
                      appointment.dw_physician_id,
                      physiciansList,
                    )}
                    stars={GetPhyRatingById(
                      appointment.dw_physician_id,
                      physiciansList,
                    )}
                  />
                </View>
                <View style={s?.itemsSeperator}>
                  <Communication
                    info={[
                      {purpose: appointment.encountertype},
                      {diagnosis: appointment.diagnosis},
                      {
                        payment:
                          appointment.patient_claim_status === 'CLOSED'
                            ? true
                            : false,
                      },
                      {
                        patient_outstanding: appointment.patient_outstanding
                          ? `$ ${appointment.patient_outstanding}`
                          : 'NA',
                      },
                      {selected: 'from Past Visits'},
                      // {
                      //   encountertype: diagnosisText.encountertype
                      //     ? `${diagnosisText.encountertype}`
                      //     : 'NA',
                      // }
                    ]}
                    // doctor={getAppointmentsByIdData.doctor}
                    date={ConvertDateToStrWithTime(
                      appointment.appt_date + ' ' + appointment.appt_time,
                    )}
                    // data={getAppointmentsByIdData.data_list}
                    onPressPay={() => {}}
                    showImages={showImages}
                    setShowImages={setShowImages}
                    showVideo={showVideo}
                    setShowVideo={setShowVideo}
                    showAudio={showAudio}
                    setShowAudio={setShowAudio}
                    showMedication={false}
                    // fetchingMedications={medicationList.fetching}
                    // medicine={medicationList}
                    data={data}
                  />
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      ) : (
        <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
          <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
        </View>
        // <></>
      )}
    </SafeAreaView>
  );
};

const createStyle = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.brandBackground,
    },
    appoinymentBlock: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    block: {
      marginTop: theme.metrics.x2_5,
    },
    itemsSeperator: {
      marginBottom: theme.metrics.x2_5,
    },
  });

export default AppointmentResultDetails;
