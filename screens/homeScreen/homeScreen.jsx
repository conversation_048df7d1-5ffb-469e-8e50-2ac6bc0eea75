import { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  BackHandler,
  Platform,
  Linking,
  SafeAreaView,
  RefreshControl,
  FlatList,
} from 'react-native';
import { Theme, useThemeContext } from '../ui/theme';
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ViewAll } from '../ui/components/ViewAll';
import { Header } from '../ui/components/Header';
import { theme } from '../ui/theme/default/theme';
import { TextComponent } from '../ui/components/TextComponent';
import { Reminder } from '../ui/components/Reminder';
import { Appointment } from '../ui/components/Appointment';
import moment from 'moment';
import { AppointmentResult } from '../ui/components/AppointmentResult';
import { useDispatch, useSelector } from 'react-redux';
import { getPatientInfo } from '../../hooks/login';
import {
  setFetchUser,
  setUserInfo,
  setUserLogin,
} from '../../stores/userReducer';
import GetAppointmentList from '../../hooks/getAppointmentList';
import GetMedicationList from '../../hooks/getMedication';
import { ConvertDateToStr, formatTime } from '../../helpers/convertDateToStr';
import { useIsFocused } from '@react-navigation/native';
import { ErrorMsgHandler } from '../ui/components/MessageHandler/messageHandler';
import getNotificationList from '../../hooks/getNotificationList';
import getPhysiciansList from '../../hooks/getPhysicianList';
import getLocationList from '../../hooks/getLocatioList';
import GetPracticeInfo from '../../helpers/getPracticeInfo';
import getPurposeList from '../../hooks/getPurposeList';
import HandleLogout from '../../helpers/logout';
import GetPhyImgById from '../../helpers/getPhysicianImgById';
import config from '../../config';
import messaging from '@react-native-firebase/messaging';
import { axiosPrivate } from '../../api/axiosPrivate';
import { CustomSwitch } from '../ui/components/CustomSwitch.js';
import CustomText from '../ui/components/customText/customText';
import HandlePracChng from '../../helpers/handlePracticeChange';
import getPharmacyList from '../../hooks/getPharmacyList';
import { settogglepractice } from '../../stores/userReducer';
import PushNotification from 'react-native-push-notification';
import { setChatRefresh } from '../../stores/userReducer';
import { setReduxPractice } from '../../stores/userPracticeReducer';
import { AxiosError } from 'axios';
import GetRsiSenMapping from '../../hooks/getRsisegmapping.js';
const HomeScreen = ({ newMessage, navigation, route }) => {
  const dispatch = useDispatch();
  const chatRefresh = useSelector(state => state.userInfo.chatrefresh);
  // const userInfo = useSelector(state => state.userInfo.userInfo);
  PushNotification.configure({
    onNotification: async function (notification) {
      if (notification.userInteraction) {
        dispatch(setChatRefresh(Number(chatRefresh) + 1));
      }
    },
    requestPermissions: Platform.OS === 'ios',
  });
  const [state, setState] = useState(false);


  const [storedTime, setStoredTime] = useState(null);


  const { s } = useThemeContext(createStyle);
  const isFocused = useIsFocused();
  // user info redux variables
  const userInfo = useSelector(state => state.userInfo.userInfo);


  const fetchingUserAPI = useSelector(state => state.userInfo.fetchingUserAPI);
  // appointment info redux variables
  const upcomingAppointments = useSelector(
    state => state.AppointmentDetails.upcomingAppointments
  );


  const previousAppointments = useSelector(
    state => state.AppointmentDetails.previousAppointments
  );

  const fetchingAPI = useSelector(
    state => state.AppointmentDetails.fetchingAPI
  );


  const pastAppointments = previousAppointments && previousAppointments
  .filter(data => moment(data.appt_date, 'YYYY-MM-DD').isBefore(moment()))
  .sort((a, b) =>
    moment(`${b.appt_date} ${b.appt_time}`, 'YYYY-MM-DD HH:mm:ss') -
    moment(`${a.appt_date} ${a.appt_time}`, 'YYYY-MM-DD HH:mm:ss')
  );


  // medication info redux variables
  const MedicationDetails = useSelector(
    state => state.MedicationDetails.MedicationDetails,
  );
  const fetchingMedicationAPI = useSelector(
    state => state.MedicationDetails.fetchingMedicationAPI,
  );

  // physicians info redux variables
  const fetchingPhysicians = useSelector(state => state.PhysicianList.fetching);

  // medication info redux variables
  const fetchingLocations = useSelector(state => state.LocationList.fetching);
  // purpose info redux variables
  const fetchingPurpose = useSelector(
    state => state.PurposeList.fetchingPurposeAPI,
  );
  // physicians list redux variable
  const physiciansList = useSelector(state => state.PhysicianList.data);

  const [modalVisible, setModalVisible] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setresponseErrorMsg] = useState(false);
  const [showInitNotification, setShowInitNotification] = useState(false);
  const accessToken = useSelector(state => state.UserToken);
  const [practice, setPractice] = useState();
  const [practiceObj, setPracticeObj] = useState();
  const [patientdata, setPatientdata] = useState();
  const [refresh, setRefresh] = useState(false);

  useEffect(() => {
    messaging().onNotificationOpenedApp(remoteMessage => {
      setShowInitNotification(() => true);
    });
  }, []);

  // useEffect(() => {


  //   fetchStoredTime();
  // }, []);
    const fetchStoredTime = async () => {
      const storedTimeString = await AsyncStorage.getItem('newapttime');


      if (storedTimeString) {
        setState(true);
        const apptTime = new Date(storedTimeString);
        setStoredTime(apptTime);
        const practice = await GetPracticeInfo();



        GetAppointmentList(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practice.preferred_practice,
          false,
        );
      }
    };
  useEffect(() => {
    if (storedTime) {
      const currentTime = new Date();
      const timeDiff = currentTime - storedTime;
      if (timeDiff >= 30000) {
        setState(false);
        AsyncStorage.removeItem('newapttime');
      } else {
        const remainingTime = 30000 - timeDiff;
        setTimeout(() => {
          AsyncStorage.removeItem('newapttime');
          setState(false);
        }, remainingTime);
      }
    }
  }, [storedTime])

  const handlePracticeChange = async evt => {
    let value;
    if (evt == 1) {
      value = 'rsi';
    } else if (evt == 2) {
      value = 'sen';
    }

    if (value !== practice) {
      setPractice(value);
      navigation.navigate('Plainloader');
      await HandlePracChng(dispatch, value);
      dispatch(settogglepractice(value));
      navigation.navigate('Home');
    }
  };

  const checkAccessToken = async () => {
    try {
      const practiceInfo = await GetPracticeInfo();
      setPractice(practiceInfo.preferred_practice);
      dispatch(setReduxPractice(practiceInfo.preferred_practice));
      setPracticeObj(practiceInfo);

      let patientInfo = {};
      if (fetchingUserAPI) {
        dispatch(setFetchUser(true));

        try {
          patientInfo = await getPatientInfo(practiceInfo.preferred_practice);
        } catch (e) {
          console.warn('getpatient error in home page', e);
          throw e;
        }
        dispatch(setFetchUser(false));
      }

      if (patientInfo.userInfo || userInfo.fname !== '') {

        if (accessToken.DeviceToken) {
          registerToken(accessToken);
        }
        if (!patientInfo.userInfo.is_terms_accepted) {
          navigation.reset({
            index: 0,
            routes: [
              {
                name: 'Terms and Conditions 1',
                params: {
                  fromScreen: 'Home',
                  preferredPractice: practiceInfo.preferred_practice,
                },
              },
            ],
          });
          // navigation.navigate('Terms And Condition 1', { fromScreen: 'Home', preferredPractice: practiceInfo.preferred_practice });
        }
        if (patientInfo.userInfo) {
          patientInfo = patientInfo.userInfo;

          dispatch(setUserInfo(patientInfo));
          dispatch(setUserLogin(true));
        }
        // Getting appointment list API
        if (!fetchingAPI) {
          // alert(1)
          GetAppointmentList(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
          GetRsiSenMapping(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
        }
        // Getting Medication list API
        if (fetchingMedicationAPI) {
          // alert(2)
          GetMedicationList(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
        }
        // Getting Physicians list API
        if (fetchingPhysicians) {
          // alert(3)
          getPhysiciansList(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
        }
        // get patient pharmacy list
        getPharmacyList(
          dispatch,
          setErrorModal,
          setresponseErrorMsg,
          practiceInfo[practiceInfo.preferred_practice],
          practiceInfo.preferred_practice,
        );
        // Getting purpose list API
        if (fetchingPurpose) {
          // alert(4)
          getPurposeList(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
        }
        // Getting Notification list API
        // getNotificationList(
        //   dispatch,
        //   setErrorModal,
        //   setresponseErrorMsg,
        //   practiceInfo[practiceInfo.preferred_practice],
        // );

        //Getting Location list API
        if (fetchingLocations) {
          // alert(5)
          getLocationList(
            dispatch,
            setErrorModal,
            setresponseErrorMsg,
            practiceInfo.preferred_practice,
          );
        }
      } else {
        console.log('home else block');
        HandleLogout(dispatch, false);
      }
    } catch (e) {
      if (e instanceof AxiosError && e.response && e.response.status === 403) {
        HandleLogout(dispatch, false);

      } else if (e instanceof AxiosError && e.response) {
        console.log(e.response);

      }
      else {
        console.warn('home catch', e);
      }
    }
  };

  const registerToken = async accessToken => {

    const practice = await GetPracticeInfo();
    const chart = practice[practice.preferred_practice];
    const preferred_practice = practice.preferred_practice;
    const transferPacket = {
      chart: chart,
      practice: preferred_practice,
      device_token: accessToken.DeviceToken,
      device_type: Platform.OS,
    };
    return axiosPrivate
      .post(`${config[config.STAGING].PATIENT_INFO}post-token`, transferPacket)
      .catch(e => {
        console.log("Error", e);
      });
  };

  useEffect(() => {
    if (isFocused) {
      checkAccessToken();
      refreshNotification();
      fetchStoredTime();
    }
  }, [isFocused]);

  const refreshNotification = async () => {
    let practice = await GetPracticeInfo();
    if (practice) {
      getNotificationList(
        dispatch,
        null,
        null,
        practice[practice.preferred_practice],
      );
    }
  };

  // function trigger when user pull down the screen to refresh the appt
  const onRefresh = async () => {
    try {
      // setRefresh(true);
      dispatch(setFetchUser(true));
      const practiceInfo = await GetPracticeInfo();
      await GetMedicationList(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practice,
      );
      await GetRsiSenMapping(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practiceInfo.preferred_practice,
      );

      await GetAppointmentList(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practice,
      )

      await getPurposeList(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practiceInfo.preferred_practice,
      );

      await getPhysiciansList(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practiceInfo.preferred_practice,
      );

      await getPharmacyList(
        dispatch,
        setErrorModal,
        setresponseErrorMsg,
        practiceInfo[practiceInfo.preferred_practice],
        practiceInfo.preferred_practice,
      );



    } catch (err) {
      console.log(err);
    } finally {
      setRefresh(false)
      dispatch(setFetchUser(false));
    }

  };


  console.log(pastAppointments,"pastAppointments");
  if (fetchingUserAPI)
    return (
      <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
        <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
      </View>
    );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView
        style={s?.container}
        showsVerticalScrollIndicator={false}
        bounces={true}
        refreshControl={
          <RefreshControl refreshing={refresh} onRefresh={onRefresh} />
        }>
        <>
          <Header
            newMessage={newMessage}
            backgroundColor
            welcomeText={userInfo.fname}
            onBurgerPress={true}
            onPracticePress={true}
            navigation={navigation}
            initNavigationStatus={showInitNotification}
          />

          {practiceObj?.rsi && practiceObj?.sen && (
            <View style={{ marginTop: 5, marginBottom: 20 }}>
              <View
                style={{
                  flexDirection: 'row',
                  // justifyContent: "space-between",
                  alignItems: 'center',
                  marginHorizontal: 15,
                }}>
                {practice && (fetchingUserAPI || fetchingAPI) ? null : (
                  <>
                    <CustomText
                      style={{
                        color: theme.colors.darkGrey,
                        ...theme.fonts.texts.h4,
                        paddingRight: 10,
                      }}>
                      Select Practice
                    </CustomText>
                    <CustomSwitch
                      selectionMode={practice == 'rsi' ? 1 : 2}
                      roundCorner={true}
                      option1={'RSI'}
                      option2={'SEG'}
                      disabled={fetchingUserAPI || fetchingAPI ? true : false}
                      onSelectSwitch={evt => handlePracticeChange(evt)}
                    />
                  </>
                )}
              </View>
            </View>
          )}
 {fetchingUserAPI || fetchingAPI ? (
      <View style={s?.noAppointments}>
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
        </View>
      </View>
    ) : upcomingAppointments && upcomingAppointments.length === 0 ? (
      <View style={s?.appointmentsBlock}>
        <View style={{ marginTop: 5, marginBottom: 20 }}>
          <ViewAll
            onPress={() => {
              navigation.navigate('Appointments', { selectView: 1 });
            }}
            text={'Upcoming Appointments'}
            disabled={true}
          />
        </View>
        <View style={s?.block}>
          <TextComponent bodyText={'None Scheduled'} />
        </View>
      </View>
    ) : (
      <View style={s?.appointmentsBlock}>
        <View style={{ marginTop: 5, marginBottom: 20 }}>
          <ViewAll
            onPress={() => {
              navigation.navigate('AppointmentsResult', { selectView: 1 });
            }}
            text={'Upcoming Appointments'}
            disabled={
              upcomingAppointments.filter(
                data =>
                  moment(data.appt_date).format('YYYY-MM-DD') >=
                  moment().format('YYYY-MM-DD'),
              ).length === 0
            }
          />
        </View>

        {!upcomingAppointments.filter(
                data =>
                  moment(data.appt_date).format('YYYY-MM-DD') >=
                  moment().format('YYYY-MM-DD'),
              ).length && (
          <View style={[s?.block]}>
            <TextComponent bodyText={'None Scheduled'} />
          </View>
        )}

<View style={{ flex: 1 }}>
  <ScrollView
    showsHorizontalScrollIndicator={false}
    contentContainerStyle={{ paddingRight: 30 }}
    horizontal
    style={s?.scrollStyle}>
    {upcomingAppointments &&
      upcomingAppointments
        .filter(
          (data) =>
            moment(data.appt_date).format('YYYY-MM-DD') >= moment().format('YYYY-MM-DD')
        )
        .sort(
          (a, b) =>
            moment(`${a.appt_date} ${a.appt_time}`) - moment(`${b.appt_date} ${b.appt_time}`)
        )
        .map((item, index) => (
          <TouchableOpacity
            disabled={state}
            onPress={() =>
              navigation.navigate('AppointmentDetails', { appointment: item })
            }
            key={item.id} // use stable key like id
            style={s?.appointment}>
            <Appointment
              date={
                moment(item.appt_date).format('DD.MM.YYYY') === moment().format('DD.MM.YYYY')
                  ? 'Today'
                  : ConvertDateToStr(item.appt_date)
              }
              time={formatTime(item.appt_time)}
              type={item.appointmenttype}
              name={
                item.physician_fname
                  ? `Dr. ${item.physician_fname} ${item.physician_lname}`
                  : 'Physician not assigned'
              }
              selected={true}
              image={
                item.dw_physician_id
                  ? GetPhyImgById(item.dw_physician_id, physiciansList)
                  : null
              }
              latitude={item.geo_lat}
              longitude={item.geo_lon}
              destination={item.location_address_1}
              alert={false}
            />
          </TouchableOpacity>
        ))}
  </ScrollView>

  {state && (
    <View style={s?.overlay}>
      <Text style={s?.overlayText}>Updating appointments...</Text>
    </View>
  )}
</View>
      </View>
    )}
          <View style={{ marginTop: 30, marginBottom: 20 }}>
            <ViewAll
              onPress={() => navigation.navigate('Medicines', { selectView: 1 })}
              text={'Medication'}
              // disabled={MedicationDetails?.ocular_med?.slice(0, 3).length === 0}
            />
          </View>
          {fetchingUserAPI || fetchingMedicationAPI ? (
            <View
              style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
              <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
            </View>
          ) : MedicationDetails?.ocular_med?.length > 0 ? (
            MedicationDetails.ocular_med.slice(0, 3).map((item, index) => {
              return (
                <View key={index} style={s?.componentBlock}>
                  <Reminder
                    image={require('./assets/medicine.png')}
                    headerText={item.name}
                    givenBy={`${item.physician_fname} ${item.physician_lname}`}
                    proportion={`${item.dose || ''} ${item.doseunit && item.doseunit !== 'Unknown' && item.dose
                      ? item.doseunit
                      : '-'
                      }`}
                    onPressReminder={() => setModalVisible(true)}
                    shadow={true}
                    startDate={
                      item.start_date !== null &&
                        item.start_date !== undefined &&
                        item.start_date !== ''
                        ? ConvertDateToStr(item.start_date)
                        : '-'
                    }
                    endDate={
                      item.stop_date !== null && item.stop_date !== undefined
                        ? ConvertDateToStr(item.stop_date)
                        : '-'
                    }
                    medicationText={item.medication_text}
                    AllData={item}
                    doctorImg={GetPhyImgById(
                      item.dw_physician_id,
                      physiciansList,
                    )}
                    isOcularMed={true}
                    pharmacy={item.pharmacynotetext}
                  />
                </View>
              );
            })
          ) : (
            <View style={s?.block}>
              <TextComponent bodyText={'No Medication'} />
            </View>
          )}
 {(!fetchingAPI && pastAppointments.length > 0 && (
  <View>
    <View style={{ marginTop: 30, marginBottom: 20 }}>
      <ViewAll
        onPress={() => navigation.navigate('AppointmentsResult', { selectView: 2 })}
        text={'Past Visits'}
        disabled={pastAppointments.length === 0}
      />
    </View>
    <FlatList
      data={pastAppointments}
      keyExtractor={(item, index) => item.patientappointmentid?.toString() || index.toString()}
      renderItem={({ item: appointment }) => (
        <AppointmentResult
          onPress={() => navigation.navigate('AppointmentResultDetails', { appointment })}
          image={GetPhyImgById(appointment.dw_physician_id, physiciansList)}
          doctor={appointment.physician_fname && appointment.physician_lname
            ? `Dr. ${appointment.physician_fname} ${appointment.physician_lname}`
            : 'Physician not assigned'}
          date={ConvertDateToStr(appointment.appt_date)}
          payed={appointment.patient_claim_status === 'CLOSED'}
          outstandingAmount={appointment.patient_outstanding}
        />
      )}
      showsVerticalScrollIndicator={true}
      nestedScrollEnabled={true}
      scrollEnabled={true}
      style={{ maxHeight: 350 }}
      contentContainerStyle={{ paddingBottom: 10 }}
    />
  </View>
)) || fetchingUserAPI || (fetchingAPI && <></>) || (
  <>
    <View style={{ marginTop: 30, marginBottom: 20 }}>
      <ViewAll
        onPress={() => navigation.navigate('AppointmentsResult', { selectView: 2 })}
        text={'Past Visits'}
        disabled={pastAppointments.length === 0}
      />
    </View>
    <View style={[s?.block, { marginTop: 10 }]}>
      <TextComponent bodyText={'No Past Visits'} />
    </View>
  </>
)}
          <ErrorMsgHandler
            HandleModal={errorModal}
            setHandleModal={setErrorModal}
            msg={responseErrorMsg}
            clickedOk={() => { }}
          />
        </>
      </ScrollView>
    </SafeAreaView>
  );
};
const createStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.brandBackground,
    },
    block: {
      justifyContent: 'center',
      alignItems: 'center',
      flex: 1,
    },
    componentBlock: {
      marginBottom: theme.metrics.x2,
      width: theme.metrics.width,
      paddingHorizontal: theme.metrics.x4,
    },
    button: {
      paddingHorizontal: theme.metrics.x4,
      marginBottom: theme.metrics.x5,
    },
    noAppointments: {
      flex: 1,
    },
    appointmentsBlock: {
      marginVertical: theme.metrics.x2,
    },
    appointment: {
      marginRight: theme.metrics.x2,
      justifyContent: 'center',
      alignItems: 'center',
    },
    button2: {
      paddingHorizontal: theme.metrics.x4,
      marginTop: theme.metrics.x5,
    },
    scrollStyle: {
      flexDirection: 'row',
      paddingLeft: theme.metrics.x4,
    },
    touchable: {
      position: 'relative',
      borderRadius: 10,
      overflow: 'hidden',
      marginBottom: 16,
    },
    overlay: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    overlayText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#2C3E50', // Elegant dark blue-gray
      textShadowColor: 'rgba(0, 0, 0, 0.15)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
      letterSpacing: 0.5,
    },
  });

export default HomeScreen;
