import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {theme} from '../../ui/theme/default/theme';
import {axiosPublic} from '../../../api/axiosPublic';
import config from '../../../config';
import PlainLoader from '../../Plainloader/PlainLoader';
import WebView from 'react-native-webview';
import axios from 'axios';
import {event} from 'react-native-reanimated';
import {axiosPrivate} from '../../../api/axiosPrivate';
import GetPracticeInfo from '../../../helpers/getPracticeInfo';
import {DefaultModal} from '../../ui/components/Modals/DefaultModal';
import HandleLogout from '../../../helpers/logout';
import {useDispatch} from 'react-redux';
import LogoutIcon from './assets/logoutIcon';
import CustomText from '../../ui/components/customText/customText';

const TermsAndConditions = ({route, navigation}) => {
  const [accepted, setAccepted] = useState(false);
  const {width, height} = Dimensions.get('window');
  const webviewRef = useRef();
  const [tac, setTac] = useState('');
  const [modal, setModal] = useState(false);
  const [inTransact, setInTransact] = useState(false);
  const [patientInfo, setPatientInfo] = useState(null);

  const {params} = route;
  const dispatch = useDispatch();

  useEffect(() => {
    (async () => {
      const termsAndCond = await axios.get(
        config[config.STAGING].COGNITO_URL + 'terms-and-conditions',
      );
      setTac(termsAndCond.data.message);
    })();
  }, []);

  const scrollIndicatorScript = `
  (function() {
    var indicator = document.createElement('div');
    indicator.innerHTML = '⬇ Scroll Down ⬇';
    indicator.style.position = 'fixed';
    indicator.style.bottom = '20px';
    indicator.style.left = '50%';
    indicator.style.transform = 'translateX(-50%)';
    indicator.style.fontSize = '18px';
    indicator.style.fontWeight = 'bold';
    indicator.style.color = '#fff';
    indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
    indicator.style.padding = '12px 18px';
    indicator.style.borderRadius = '30px';
    indicator.style.boxShadow = '0px 4px 10px rgba(0, 0, 0, 0.2)';
    indicator.style.zIndex = '9999';
    indicator.style.cursor = 'pointer';
    indicator.style.transition = 'all 0.3s ease';
    indicator.style.animation = 'bounce 1.5s infinite';
    indicator.style.opacity = '1';
    document.body.appendChild(indicator);

    var isScrolling = false;
    var scrollAnimationFrame;
    var originalText = '⬇ Scroll Down ⬇';
    var isAtBottom = false;

    // Function to check if user is at the bottom of the page
    function checkScrollPosition() {
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      var windowHeight = window.innerHeight;
      var documentHeight = document.documentElement.scrollHeight;
      var threshold = 50; // Show button when user scrolls up 50px from bottom

      var atBottom = scrollTop + windowHeight >= documentHeight - threshold;

      if (atBottom && !isAtBottom) {
        // User reached bottom - hide indicator
        isAtBottom = true;
        indicator.style.opacity = '0';
        indicator.style.transform = 'translateX(-50%) scale(0.8)';
        setTimeout(() => {
          if (isAtBottom) {
            indicator.style.display = 'none';
          }
        }, 300);
      } else if (!atBottom && isAtBottom) {
        // User scrolled up from bottom - show indicator
        isAtBottom = false;
        indicator.style.display = 'block';
        indicator.style.opacity = '1';
        indicator.style.transform = 'translateX(-50%) scale(1)';
      }
    }

    function smoothScrollToBottom() {
      let currentScroll = window.scrollY;
      let targetScroll = document.body.scrollHeight - window.innerHeight;
      let duration = 8000; // Reduced duration for smoother experience
      let startTime = null;
      isScrolling = true;

      // Keep the same scroll down icon during auto-scrolling
      indicator.innerHTML = originalText;
      indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
      indicator.style.animation = 'pulse 1s infinite';

      function scrollStep(timestamp) {
        if (!startTime) startTime = timestamp;
        let progress = timestamp - startTime;
        let percentage = Math.min(progress / duration, 1);

        // Use easeInOutCubic for smoother animation
        let easePercentage = percentage < 0.5
          ? 4 * percentage * percentage * percentage
          : 1 - Math.pow(-2 * percentage + 2, 3) / 2;

        if (!isScrolling) {
          resetIndicator();
          return;
        }

        window.scrollTo(0, currentScroll + (targetScroll - currentScroll) * easePercentage);

        if (progress < duration && percentage < 1) {
          scrollAnimationFrame = requestAnimationFrame(scrollStep);
        } else {
          // Scrolling completed
          isScrolling = false;
          resetIndicator();
          // Check position after auto-scroll completes
          setTimeout(checkScrollPosition, 100);
        }
      }

      scrollAnimationFrame = requestAnimationFrame(scrollStep);
    }

    function resetIndicator() {
      indicator.innerHTML = originalText;
      indicator.style.background = 'linear-gradient(45deg, #007BFF, #00C6FF)';
      indicator.style.animation = 'bounce 1.5s infinite';
      isScrolling = false;
    }

    indicator.addEventListener('click', function() {
      if (!isScrolling && !isAtBottom) {
        smoothScrollToBottom();
      }
    });

    // Add scroll event listener to check position
    window.addEventListener('scroll', function() {
      if (!isScrolling) {
        checkScrollPosition();
      }
    });

    function stopScrolling() {
      if (isScrolling) {
        isScrolling = false;
        cancelAnimationFrame(scrollAnimationFrame);
        resetIndicator();
        // Check position after stopping
        setTimeout(checkScrollPosition, 100);
      }
    }

    // Stop scrolling on user interaction
    ['mousedown', 'wheel', 'keydown', 'touchstart'].forEach(event => {
      window.addEventListener(event, stopScrolling);
    });

    // Initial position check
    setTimeout(checkScrollPosition, 500);

    var style = document.createElement('style');
    style.innerHTML = \`
      @keyframes bounce {
        0%, 100% { transform: translateX(-50%) translateY(0); }
        50% { transform: translateX(-50%) translateY(-10px); }
      }
      @keyframes pulse {
        0%, 100% { transform: translateX(-50%) scale(1); }
        50% { transform: translateX(-50%) scale(1.05); }
      }
    \`;
    document.head.appendChild(style);
  })();
`;

  const isCloseToBottom = ({layoutMeasurement, contentOffset, contentSize}) => {
    const paddingToBottom = 20;
    return (
      layoutMeasurement.height + contentOffset.y >=
      contentSize.height - paddingToBottom
    );
  };

  const onLayout = event => {
    const {x, y, height, width} = event.nativeEvent.layout;
  };

  const handleSuccess = async () => {
    navigation?.replace('LoginScreen');
  };

  const handleDeny = () => {
    if (params.fromScreen === 'Home') {
      setModal(true);
    } else {
      navigation.goBack();
    }
  };

  return (
    <View
      style={{
        flex: 1,
        marginLeft: 10,
        marginRight: 10,
        backgroundColor: theme.colors.brandBackground,
      }}>
      <WebView
        incognito={true}
        ref={ref => (webviewRef.current = ref)}
        style={{flex: 1, width: '100%'}}
        originWhitelist={['*']}
        injectedJavaScript={scrollIndicatorScript}
        source={{uri: config[config.STAGING].TERMS_AND_CONDITIONS}}
        onLayout={onLayout}
        automaticallyAdjustContentInsets
        setBuiltInZoomControls={false}
        onMessage={event => {}}
        renderLoading={PlainLoader}
        startInLoadingState
        scalesPageToFit={true}
        bounces={false}
        onScroll={event => {
          const condition =
            event.nativeEvent.contentOffset.y * 1.03 >
            event.nativeEvent.contentSize.height -
              event.nativeEvent.layoutMeasurement.height;
          if (condition) {
            setAccepted(true);
          } else {
            setAccepted(false);
          }
        }}
      />
      <View style={{flex: 0.1, flexDirection: 'row', paddingBottom: 30}}>
        <TouchableOpacity
          disabled={inTransact}
          onPress={handleDeny}
          style={{
            backgroundColor: theme.colors.gray,
            borderRadius: 5,
            flex: 0.8,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <CustomText
            style={{fontSize: 14, color: '#FFF', alignSelf: 'center'}}>
            I deny
          </CustomText>
        </TouchableOpacity>
        <View style={{flex: 0.2}}></View>
        <TouchableOpacity
          disabled={!accepted || inTransact}
          onPress={handleSuccess}
          style={[
            {
              borderRadius: 5,
              flex: 0.8,
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'row',
            },
            accepted
              ? {backgroundColor: theme.colors.brandPrimary}
              : {backgroundColor: theme.colors.gray},
          ]}>
          <CustomText
            style={{fontSize: 14, color: '#FFF', alignSelf: 'center'}}>
            I agree
          </CustomText>
          {inTransact && <ActivityIndicator />}
        </TouchableOpacity>
      </View>
      <DefaultModal
        onBottomButtonPress={() => {
          setModal(false);
          HandleLogout(dispatch, false);
          // navigation.navigate('LoginScreen');
        }}
        onBottomButtonText={'Log out'}
        disablePhoneIcon
        onTopButtonPress={() => setModal(false)}
        onTopButtonText={'Continue'}
        Icon={LogoutIcon}
        text={'Please accept terms and conditions to continue using the app'}
        modalVisible={modal}
        setModalVisible={item => setModal(item)}
      />
    </View>
  );
};

export default TermsAndConditions;
