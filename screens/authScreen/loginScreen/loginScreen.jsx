import { useState, useRef , useEffect} from 'react';
import {
  View,
  StyleSheet,
  Keyboard,
  SafeAreaView,
  TouchableWithoutFeedback,
  ActivityIndicator,
  ScrollView,
  Text,
} from 'react-native';
import { TextComponent } from '../../ui/components/TextComponent';
import { Theme, useThemeContext } from '../../ui/theme';
import { Button } from '../../ui/components/Button';
import { theme } from '../../ui/theme/default/theme';
import { OurTextInput } from '../../ui/components/OurTextInput';
import { ErrorMsgHandler } from '../../ui/components/MessageHandler/messageHandler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import TopBackground from '../assets/TopBackground';
import { validateDOB, validatePhoneNumber , isAbove18Years} from '../../../helpers/ValidationHelper';
import { Picker } from '../../ui/components/Picker';
import moment from 'moment';
import { ConvertDateToStr } from '../../../helpers/convertDateToStr';
import { otpLoginHook } from '../../../hooks/otpLogin';
import CustomText from '../../ui/components/customText/customText';
import { captureException } from '@sentry/react-native';
import DeviceInfo from 'react-native-device-info';
const LoginScreen = ({ navigation }) => {
  const { s } = useThemeContext(createStyle);
  const [preloader, setPreloader] = useState(false);
  const scrollViewRef = useRef();
  const [DOB, setDOB] = useState(new Date())
  const [DOBModal, setDOBModal] = useState(false)
  const [mobilePhone, setMobilePhone] = useState("")
  const [errorModal, setErrorModal] = useState(false);
  const [responseErrorMsg, setresponseErrorMsg] = useState(false);
  const [successModal, setSuccessModal] = useState(false);
  const [responseSuccessMsg, setResponseSuccessMsg] = useState(false);
  const [hideErrors, setHideErrors] = useState(true);
  const [underageError, setUnderageError] = useState(false);

  const HandleLogin = async () => {
    try {
      const isDOBValid = validateDOB(DOB); 
      const is18yrOlder = isAbove18Years(DOB); 
      setHideErrors(true);
      setUnderageError(false);
      if (!isDOBValid) {
        setHideErrors(false);
        return; 
      }
      if (!is18yrOlder) {
        setUnderageError(true);
        return; 
      }
      if (validatePhoneNumber(mobilePhone)) {
        setPreloader(true);
        let patientInfo = await otpLoginHook(
          mobilePhone,
          moment(DOB).format('YYYY-MM-DD'),
          setErrorModal,
          setresponseErrorMsg,
          setSuccessModal,
          setResponseSuccessMsg,
        );
        if (patientInfo.status === 200) {
          navigation.navigate("LoginOtpScreen", {
            DOB: moment(DOB).format('YYYY-MM-DD'),
            mobile: mobilePhone
          });
          return;
        }
        setresponseErrorMsg(patientInfo.data.message);
        setErrorModal(true);
      }
    } catch (err) {
      console.log(err);
      if (err.response.data.msg === "patient not found" && err.response.data.status === 404) {
        navigation.navigate("LoginErrorScreen");
        return;
      }
      if (err.response.data.status === 403) {
        captureException(err);
        setresponseErrorMsg("This app is only accessible within the United States. Please try again from a supported region.");
        setErrorModal(true);
      }
    } finally {
      setPreloader(false);
    }
  }; 

  if (preloader) {
    return (
      <View style={s?.preloaderContainer}>
        <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
      </View>
    );
  }

  //only number input 
  const PhoneNumberSet = (num) => {
    const numericText = num.replace(/[^0-9]/g, "");
    setMobilePhone(numericText)
  }



  return (
    <View style={s?.container}>
      <View style={{ flex: 1, position: 'absolute', left: 0 }}>
        <TopBackground />
      </View>
      <KeyboardAwareScrollView style={{ flex: 1 }} bounces={false}>
        <ScrollView ref={scrollViewRef} style={{ flex: 1, height: '100%' }} bounces={false}>
          <TouchableWithoutFeedback
            onPress={() => Keyboard.dismiss()}
            style={{ flex: 1 }}
          >
            <SafeAreaView style={{ flex: 1, margninTop: 15 ,justifyContent: 'space-between'}} >
              <View style={[s?.mainCont]}>
                <TextComponent
                  headerText={'Welcome Back'}
                  bodyText={'We are glad to see you again!'}
                />
                <View style={{ height: 60 }} />
                {/* mobile number input  */}
                <OurTextInput
                  value={mobilePhone}
                  placeholder={'************'}
                  prefix={'+1 '}
                  title="Phone number"
                  paddingCustomLeft={0}
                  keyboardType={'number-pad'}
                  errorText={'Phone number must be minimum 10 digits'}
                  validationOk={hideErrors || validatePhoneNumber(mobilePhone)}
                  customHeight={80}
                  paddingCustomRight={0}
                  maxLength={10}
                  onChangeText={PhoneNumberSet}
                  placeholderColor={theme.colors.inputGray}
                />
                {!(hideErrors || validatePhoneNumber(mobilePhone)) && (
                  <>
                    <View style={{ width: 2, paddingBottom: 20 }} />
                  </>
                )}
                {/* DOB Picker */}
                <View style={{ height: 10 }} />
                <Picker
                  onPress={() => {
                    setDOBModal(true);
                  }}
                  title={'Date of birth'}
                  // maxDate={new Date()}
                  text={
                    moment(DOB).format('MM.DD.YYYY') ===
                      moment().format('MM.DD.YYYY')
                      ? ''
                      : ConvertDateToStr(DOB)
                  }
                  modal={DOBModal}
                  placeholder={'Date of birth'}
                  setOpen={setDOBModal}
                  setBirthday={(event) => {
                    console.log("event", event);
                    console.log("test", moment(event).format('YYYY-MM-DD'));
                    setDOB(event)
                  }}
                  validationOk={hideErrors || validateDOB(DOB)}
                  birthDay={DOB}
                />
                {!(hideErrors || validateDOB(DOB)) && (
                  <View style={{
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    alignContent: 'flex-start',
                    width: '90%'
                  }}>
                    <CustomText style={s?.errorStyle}>Please Enter the valid DOB</CustomText>
                    <View style={{ width: 2, paddingBottom: 20 }} />
                  </View>
                )}
                {underageError && (
                 <View style={{
                  flexDirection: 'column',
                  justifyContent: 'flex-start',
                  alignContent: 'flex-start',
                  width: '90%'
                }}>
                <CustomText style={s?.errorStyle}>You must be 18 years or older to use this application</CustomText>
                <View style={{ width: 2, paddingBottom: 20 }} />
              </View>
              )}

                <View style={{ height: 15 }} />
              </View>
              <View style={{ height: 60, width: 2 }} />

              <View style={[s?.buttonCont]}>
                {/* {!keyboardOpened && ( */}
                <>
                  <Button
                    onPress={() => { HandleLogin() }}
                    //   load={loginFetching}
                    fontWeight="600"
                    text="Sign in"
                    backgroundColor={theme.colors.brandPrimary}
                  />
                </>
                {/* )} */}
              </View>

            </SafeAreaView>
            
          </TouchableWithoutFeedback>
          <View style={s?.versionContainer}>
               <Text style={s?.versionText}>Version {DeviceInfo.getVersion()}</Text>
                  </View>
          <ErrorMsgHandler
            HandleModal={errorModal}
            setHandleModal={setErrorModal}
            msg={responseErrorMsg}
            clickedOk={() => { }}
          />
          <ErrorMsgHandler
            HandleModal={successModal}
            setHandleModal={setSuccessModal}
            msg={responseSuccessMsg}
            clickedOk={() => { navigation.navigate('Terms and Conditions', { fromScreen: 'LoginScreen', mobilePhone: mobilePhone }) }}
          />
        </ScrollView>
      </KeyboardAwareScrollView>
    </View>
  );
};

const createStyle = (theme: Theme) =>
  StyleSheet.create({
    container: {
      width: theme.metrics.width,
      height: '100%',
      backgroundColor: theme.colors.brandBackground,
    },
    textBlock: {
      // width: '100%',
      // paddingHorizontal: theme.metrics.x4,
      // marginVertical: theme.metrics.x5,
      flexDirection: 'row',
      alignItems: 'center',
      marginLeft: 20,
      justifyContent: 'center',
      textAlign: 'center',
    },
    textRegular: {
      fontSize: 14,
      fontWeight: '400',
      color: theme.colors.black,
      marginLeft: 10,
    },
    buttonCont: {
      alignItems: 'center',
      alignSelf: 'center',
      width: theme.metrics.width - 30,
    },
    versionContainer: {
      alignSelf: 'center',
      paddingBottom: 20, 
      marginTop: 'auto', 
      marginBottom: 60,  
    },
    versionText: {
      fontSize: 14,
      color: theme.colors.gray,
    },
    mainCont: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      marginTop: '20%',
    },
    link: {
      color: theme.colors.brandPrimary,
      fontSize: 16,
      fontWeight: '600',
    },
    notAMember: {
      fontWeight: '800',
      fontSize: 16,
      color: '#7D7D7D',
      // marginVertical: 5,
      alignItems: 'center',
      justifyContent: 'center'
    },
    preloaderContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    signUpText: {
      marginTop: 35,
      marginHorizontal: 10,
      fontWeight: '600',
      color: theme.colors.brandPrimary,
    },
    hintText: {
      fontSize: 12,
      color: theme.colors.gray,
      // textAlign: 'left',
      padding: 0,
      marginLeft: 0,
      marginTop: -8,
      marginBottom: 15,
    }, errorStyle: {
      ...theme.fonts.texts.bodySmallRegular,
      color: '#FF6771',
      textAlign: 'left',
      marginLeft: 5
    },
  });

export default LoginScreen;
