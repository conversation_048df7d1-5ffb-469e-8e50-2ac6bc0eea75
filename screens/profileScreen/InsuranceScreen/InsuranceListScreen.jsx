import { View, Text, ScrollView, StyleSheet, SafeAreaView, Keyboard, TouchableOpacity, TouchableWithoutFeedback, Dimensions, ActivityIndicator } from 'react-native'
import React, { useEffect, useState } from 'react'
import GetPracticeInfo from '../../../helpers/getPracticeInfo'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { Theme, useThemeContext } from '../../ui/theme';
import { Header } from '../../ui/components/Header'
import { useSelector } from 'react-redux'
import { OurTextInput } from '../../ui/components/OurTextInput'
import { theme } from '../../ui/theme/default/theme'
import CustomText from '../../ui/components/customText/customText'
import { Button } from '../../ui/components/Button'
import { <PERSON><PERSON>r<PERSON>g<PERSON>and<PERSON>, SuccessMsgHandler } from '../../ui/components/MessageHandler/messageHandler'
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import DatePicker from 'react-native-date-picker';
import { axiosPrivate } from '../../../api/axiosPrivate';
import config from '../../../config';


const InsuranceListScreen = (props) => {
    const { height, width } = Dimensions.get("window")
    const { s } = useThemeContext(createStyle);
    const [practiceInfo, setPracticeInfo] = useState()
    const { userInfo } = useSelector(state => state.userInfo);
    const [payer1, setPayer1] = useState()
    const [payer2, setPayer2] = useState()
    const [payer3, setPayer3] = useState()
    const [loader, setLoader] = useState(false)
    const [addInsuranceState, setAddInsuranceState] = useState(false)
    const [dobValid, setDobValid] = useState(false)
    const [date, setDate] = useState(new Date())
    const [open, setOpen] = useState(false)
    const [dobWarning, setDobWarning] = useState(false)
    const [dobChange, setDobChange] = useState(false)
    const [loader1, setLoader1] = useState(false)
    let preferred_practice = useSelector(state => state.reduxPractice.reduxPractice)
    let chartId = userInfo.chart_number

    // set practice info  
    useEffect(() => {
        (async () => {
            setLoader(true)
            console.log(userInfo);
            const data = await GetPracticeInfo()
            setPracticeInfo(data);
            setLoader(false)
        })()
    }, []);

    useEffect(() => {
        console.log("adad", userInfo)
        userInfo.payer1_name || userInfo.payer1_name !== null ? setPayer1({
            Name: userInfo.payer1_name,
            policy: userInfo.payer1_policy
        }) : setPayer1({});

        userInfo.payer2_name || userInfo.payer2_name !== null ? setPayer2({
            Name: userInfo.payer2_name,
            policy: userInfo.payer2_policy
        }) : setPayer2({});

        userInfo.payer3_name || userInfo.payer3_name !== null ? setPayer3({
            Name: userInfo.payer3_name,
            policy: userInfo.payer3_policy
        }) : setPayer3({});
    }, [practiceInfo])

    const handleBackPress = () => {
        props.navigation.goBack();
    }

    const addInsurance = () => {
        props.navigation.replace('AddInsuranceScreen')
    }

    const formatDate = () => {
        const d = date
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const day = d.getDate().toString().padStart(2, '0');
        const year = d.getFullYear();
        return `${month}-${day}-${year}`;
    };
    const validateDOB = (date) => {
        return !(new Date(date).toLocaleDateString() === new Date().toLocaleDateString()) && !(new Date(date) > new Date());
      };
    
      const handleConfirm = (selectedDate) => {
        if (validateDOB(selectedDate)) {
          setOpen(false);
          setDate(selectedDate);
          setDobChange(true);
          setDobWarning(false);
        } else {
          setDobWarning(true)
          setDobChange(false)
        }
      };
    
    const dobValidater = async (dob) => {
        setLoader1(true)
        setDobWarning(false)
        console.log("eve", {
            practice: preferred_practice,
            chart_number: chartId,
            dob: `${dob}`
        });

        await axiosPrivate
            .post(`${config[config.STAGING].PATIENT_INFO}dob-validation`, {
                practice: preferred_practice,
                chart_number: chartId,
                dob: `${dob}`
            })
            .then(response => {
                if (response.data.status == 200 & response.data.message == 'Authorized') {
                    console.log('res for dob val:', response.data.message)
                    setDobValid(true)
                    setLoader1(false)
                }
                else {
                    console.log('val:', response.data.message)
                    setDobWarning(true)
                    setLoader1(false)
                }
            })
            .catch(err => {
                console.log(err)
                setLoader1(false)
            });
    }

    if (loader) {
        return <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <ActivityIndicator size="large" color={theme.colors.brandPrimary} />
        </View>
    }

    return (
        <KeyboardAwareScrollView bounces={false}>
      <DatePicker
        modal
        mode='date'
        open={open}
        date={date}
        onConfirm={(d) => handleConfirm(d)}
        onCancel={() => {
          setOpen(false)
        }}
      />
 
            { <ScrollView style={s?.container} bounces={false}>
                <TouchableWithoutFeedback
                    onPress={() => Keyboard.dismiss()}
                    style={{ flex: 1 }}>
                    <SafeAreaView>
                        <Header
                            backgroundColor
                            onBackPress={() => handleBackPress()}
                            title="Insurance"
                        />
                        {(payer1 && payer1.Name) &&
                            <View style={{ backgroundColor: theme.colors.white, ...theme.shadows.sh1, marginLeft: 8, marginRight: 8, paddingVertical: 10, marginTop: 10 }} key={1}>
                                <CustomText style={{ paddingHorizontal: 20, fontSize: 18, color: theme.colors.brandPrimary }} >Primary Insurance</CustomText>
                                <OurTextInput
                                    value={payer1.Name}
                                    placeholder={!payer1.Name ? 'NA' : 'Payer Name'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    title={'Payer Name'}
                                    editable={false}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    customHeight={100}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    multiline={true}
                                />
                                <OurTextInput
                                    value={payer1.policy}
                                    placeholder={!payer1.policy ? 'NA' : 'Policy Number'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    editable={false}
                                    title={'Policy Number'}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    multiline
                                />
                                <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingHorizontal: 20, marginTop: 10 }}>
                                    <TouchableOpacity style={s?.updateBtn} onPress={() => {
                                        props.navigation.replace("EditInsuranceDataScreen", {
                                            sequenceNumber: 1
                                        })
                                    }}>
                                        <Text style={[s?.buttonText, { color: 'white' }]}>Update Insurance</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        }
                        {(payer2 && payer2.Name) &&
                            <View style={{ backgroundColor: theme.colors.white, ...theme.shadows.sh1, marginLeft: 8, marginRight: 8, paddingVertical: 10, marginTop: 10 }} key={1}>
                                <CustomText style={{ paddingHorizontal: 20, fontSize: 18, color: theme.colors.brandPrimary }}> Secondary Insurance</CustomText>
                                <OurTextInput
                                    value={payer2.Name}
                                    placeholder={!payer2.Name ? 'NA' : 'Payer Name'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    title={'Payer Name'}
                                    editable={false}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    customHeight={100}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    multiline={true}
                                />
                                <OurTextInput
                                    value={payer2.policy}
                                    placeholder={!payer2.policy ? 'NA' : 'Policy Number'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    title={'Policy Number'}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    editable={false}
                                    multiline
                                />
                                <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingHorizontal: 20, marginTop: 10 }}>
                                    <TouchableOpacity style={s?.updateBtn} onPress={() => {
                                        props.navigation.replace("EditInsuranceDataScreen", {
                                            sequenceNumber: 2
                                        })
                                    }}>
                                        <Text style={[s?.buttonText, { color: 'white' }]}>Update Insurance</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        }
                        {(payer3 && payer3.Name) &&
                            <View style={{ backgroundColor: theme.colors.white, ...theme.shadows.sh1, marginLeft: 8, marginRight: 8, paddingVertical: 10, marginTop: 10 }} key={1}>
                                <CustomText style={{ paddingHorizontal: 20, fontSize: 18, color: theme.colors.brandPrimary }} >Tertiary Insurance</CustomText>
                                <OurTextInput
                                    value={payer3.Name}
                                    placeholder={!payer3.Name ? 'NA' : 'Payer Name'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    title={'Payer Name'}
                                    editable={false}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    customHeight={100}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    multiline={true}
                                />
                                <OurTextInput
                                    value={payer3.policy}
                                    placeholder={!payer3.policy ? 'NA' : 'Policy Number'}
                                    onChangeText={text => {
                                        console.log(text);
                                    }}
                                    title={'Policy Number'}
                                    paddingCustomLeft={0}
                                    paddingCustomRight={0}
                                    bgColor={theme.colors.white}
                                    validationOk={true}
                                    multiline
                                />
                                <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingHorizontal: 20, marginTop: 10 }}>
                                    <TouchableOpacity style={s?.updateBtn} onPress={() => {
                                        props.navigation.replace("EditInsuranceDataScreen", {
                                            sequenceNumber: 3
                                        })
                                    }}>
                                        <Text style={[s?.buttonText, { color: 'white' }]}>Update Insurance</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        }
                        {!(userInfo.payer1_name && userInfo.payer2_name && userInfo.payer3_name) &&
                            <View style={s?.button}>
                                <Button
                                    text={
                                        'Add Insurance'
                                    }
                                    shadow
                                    color={theme.colors.white}
                                    backgroundColor={theme.colors.brandPrimary}
                                    onPress={addInsurance}
                                />
                            </View>
                        }
                    </SafeAreaView>
                </TouchableWithoutFeedback>

            </ScrollView>}
        </KeyboardAwareScrollView>
    )
}

const createStyle = (theme: Theme) =>
    StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: theme.colors.brandBackground,
            height: '100%'
        },
        textBlock: {
            width: '100%',
            paddingHorizontal: theme.metrics.x4,
            marginVertical: theme.metrics.x5,
            flexDirection: 'row',
            alignItems: 'center',
        },
        textRegular: {
            fontSize: 16,
            fontWeight: '400',
            color: theme.colors.black,
            marginLeft: 10,
        },
        dataPicker: {
            position: 'absolute',
            bottom: Platform.OS === 'ios' ? 20 : 8,
            left: Platform.OS === 'ios' ? 20 : 6,
            // flexDirection: 'row',
            width: '90%'
        },
        componentContainer: {
            width: '100%',
            marginTop: 30,
        },
        block: {
            justifyContent: 'space-between',
            alignItems: 'center',
            flex: 1,
            bottom: 10,
        },
        block2: {
            width: '100%',
            marginBottom: 12
        },
        button: {
            paddingHorizontal: theme.metrics.x4,
            marginBottom: 10,
            marginTop: 10,
            // position:'absolute',
        },
        updateBtn: {
            padding: 10,
            backgroundColor: theme.colors.brandPrimary,
            width: '100%',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 10,
        },
        buttonContainer: {
            // width: theme.metrics.width,
            borderRadius: 8,
            ...theme.shadows.sh1,
            height: 57,
            backgroundColor: theme.colors.white,
            flexDirection: 'row',
            alignItems: 'center',
            paddingLeft: 15,
            marginBottom: 10,
            justifyContent: 'space-between',
        },
        buttonText: {
            color: theme.colors.darkGrey,
            ...theme.fonts.texts.bodyBigSemibold,
        },
        buttonBlock: {
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
        },
    });

const styles = StyleSheet.create(
    {
        container: {
            marginTop: '50%',
            flex: 1,
            gap: 40,
            justifyContent: 'center',
            // alignItems: 'center',
        },
        buttonParentContainer: {
            flex: 1,
            flexDirection: 'row',
            justifyContent: 'space-evenly',
            // marginTop:'40%'
        },
        buttonContainer: {
            width: '40%',
        },
        headingText: {
            // marginTop:'10%',
            fontSize: 18,
            textAlign: 'center',
            margin: 10,
            color: '#333333',
            fontWeight: '500'
        },
        dateText: {
            width: '80%',
            // marginTop:'40%',
            fontSize: 16,
            textAlign: 'center',
            fontWeight: '400',
            color: theme.colors.brandPrimary,
            borderWidth: 1.5,
            borderRadius: 4,
            padding: 15,
            borderColor: theme.colors.brandPrimary
        },
        dateContainer: {
            alignItems: 'center',
        },
        toastText: {
            fontSize: 16,
            textAlign: 'center',
            color: 'red',
            fontWeight: '400',
            marginBottom: '15%'
        }
    }
)

export default InsuranceListScreen