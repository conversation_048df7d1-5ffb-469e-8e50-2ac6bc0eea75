import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  Image,
} from 'react-native';
import Video, {SelectedTrackType} from 'react-native-video';
import {Button} from '../ui/components/Button';
import {theme} from '../ui/theme/default/theme';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {TextComponent} from '../ui/components/TextComponent';
import Back from '../ui/components/Header/assets/back';
import config from '../../config';
import Orientation from 'react-native-orientation-locker';

const OnBoardingVideoScreen = ({navigation}) => {
  const [loader, setLoader] = useState(true);
  const [continueBtn, setContinueBtn] = useState(false);
  const [videoLoader, setVideoLoader] = useState(false);

  const handleSkip = async () => {
    try {
      await AsyncStorage.setItem('videoCard', 'true');
      navigation?.replace('Terms and Conditions');
    } catch (err) {
      console.log(err);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    const checkdata = async () => {
      const data = await AsyncStorage.getItem('videoCard');
      if (data === 'true') {
        navigation?.replace('Terms and Conditions');
        return;
      }
      setLoader(false);
    };
    checkdata();
  }, []);

  if (loader) {
    return (
      <SafeAreaView style={styles.centeredContainer}>
        <ActivityIndicator color={theme.colors.brandPrimary} size="large" />
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
    <View >
      {/* Header with Skip Button */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Back />
          </TouchableOpacity>
        </View>
      </View>

      {/* Video and Image */}
      <View  >
        {/* Image */}
        <View style={styles.imageContainer}>
          <Image
            source={require('./assets/IconOnly.png')}
            style={styles.image}
          />
          <TextComponent headerText="Clear HealthCare" />
        </View>

        {/* Video */}
        <View style={styles.videoWrapper}>
          <Video
            source={{
              uri: config[config.STAGING].INTRO_VIDEO,
            }} // Replace with your video URL
            style={styles.video}
            controls
            resizeMode="contain"
            useNativeControls={true}
            audioOnly={false}
            // ignoreSilentSwitch="ignore"
            ignoreSilentSwitch={'ignore'}
            selectedAudioTrack={{type: SelectedTrackType.SYSTEM}}
            onFullscreenPlayerWillPresent={() => {
              Orientation.unlockAllOrientations();
            }} // Allow both orientations during fullscreen
            onFullscreenPlayerWillDismiss={() => Orientation.lockToPortrait()}
            paused={false}
            bufferConfig={{
              minBufferMs: 2500, // Minimum buffer before playback starts
              maxBufferMs: 3000, // Maximum buffer allowed
              bufferForPlaybackMs: 2500, // Buffer required to start playback
              bufferForPlaybackAfterRebufferMs: 2500, // Buffer required after rebuffering
            }}
            onEnd={() => setContinueBtn(true)}
            onError={e => console.log(e)}
            onLoadStart={() => setVideoLoader(true)} // Show loader when video starts loading
            onLoad={() => setVideoLoader(false)} // Hide loader when video has loaded
            automaticallyWaitsToMinimizeStalling={false}
          />

          {/* Loader inside Video */}
          {videoLoader && (
            <View style={styles.loaderOverlay}>
              <ActivityIndicator color={theme.colors.lochmara} size="large" />
            </View>
          )}
        </View>
      </View>

    </View>
    <View style={{ marginTop: "10%" }}>
        {/* Continue Button */}
     
          <View style={styles.buttonContainer}>
            {/* <Button
              text="Continue"
              shadow
              color="#fff"
              backgroundColor={theme.colors.brandPrimary}
              onPress={handleSkip}
            /> */}
          </View>
        
        <TouchableOpacity
          onPress={handleSkip}>
          <Text style={[styles.text]}>Skip</Text>
        </TouchableOpacity>
      </View>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.brandBackground,
  },
  centeredContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    position: 'absolute',
    top: 10,
    width: '100%',
    zIndex: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  skipButton: {
    justifyContent: 'center', // Centers vertically
    alignItems: 'center',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center', // Centers vertically
    alignItems: 'center', // Centers horizontally
    width: '100%',
    height: '100%',
  },
  block2: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: theme.metrics.x7,
  },
  text: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.lochmara,
  },
  imageContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: "20%"
  },
  image: {
    height: 100,
    width: 100,
    marginBottom: 10,
    resizeMode: 'contain',
  },
  videoWrapper: {
    width: '100%',
    height: 200,
    // position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  loaderOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.5)', // Semi-transparent background
  },
  buttonContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default OnBoardingVideoScreen;
