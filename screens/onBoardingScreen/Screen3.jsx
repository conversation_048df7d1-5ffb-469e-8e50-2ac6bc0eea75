import React, { useEffect, useState } from 'react';
import {Image, View, Text, Dimensions, FlatList} from 'react-native';
// import {FlatList as FlatList2} from 'react-native-gesture-handler'
import {FlatList as FlatList2} from 'react-native-gesture-handler';
import {theme} from '../ui/theme/default/theme';
import CustomText from '../ui/components/customText/customText';
import axios from 'axios';
import config from '../../config';
const rsiImage1 = require('./assets/rsi/gupta.webp');
const rsiImage2 = require('./assets/rsi/myers.webp');
const rsiImage3 = require('./assets/rsi/shuler.webp');
const rsiImage4 = require('./assets/rsi/tarantola.webp');
const rsiImage5 = require('./assets/rsi/baranano.webp');
const rsiImage6 = require('./assets/rsi/peters.webp');
const rsiImage8 = require('./assets/rsi/k-pierce.webp');
const rsiImage7 = require('./assets/rsi/ozkok.jpg');

const segImage1 = require('./assets/seg/alic.jpg');
const segImage2 = require('./assets/seg/dr_corder.webp');
const segImage3 = require('./assets/seg/dr-kidd.webp');
const segImage4 = require('./assets/seg/mcdaniel.webp');
const segImage5 = require('./assets/seg/nguyen.webp');
const segImage6 = require('./assets/seg/ray.webp');
const segImage7 = require('./assets/seg/rojas.jpg');
const segImage8 = require('./assets/seg/dr_rowland.webp');
const segImage9 = require('./assets/seg/wolkart.webp');
const segImage10 = require('./assets/seg/wilder.webp');
const segImage11 = require('./assets/seg/pzloty.jpg');
const segImage12 = require('./assets/seg/glusman.webp');
const segImage13 = require('./assets/seg/danielle.webp');
const segImage14 = require('./assets/seg/rich.webp');
const segImage15 = require('./assets/seg/slade.webp');
const segImage16 = require('./assets/seg/jeffrey.webp');
const segImage17 = require('./assets/seg/sugg.webp');
const segImage18 = require('./assets/seg/weaver.webp');
const segImage19 = require('./assets/seg/wilson.webp');
const segImage20 = require('./assets/seg/klabo.webp');

const rsi_pract = require('../../assets/images/png/rsi-img.png');
const sen_pract = require('../../assets/images/png/sen-img.png');
const alic = require('./assets/alic.jpg');
const donna = require('./assets/dr_corder.webp');
const mcdaniel = require('./assets/mcdaniel-1.webp');
const drKidd = require('./assets/dr-kidd.webp');
const ray = require('./assets/ray.webp');
const gupta = require('./assets/gupta.webp');
const myers = require('./assets/myers-1.webp');
const maggie = require('./assets/shuler.webp');
const tarantola = require('./assets/tarantola.webp');
const baranano = require('./assets/baranano.webp');

const Screen3 = () => {

  // const rsi = [
  //   {
  //     name: 'Sunil Gupta, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage1,
  //   },
  //   {
  //     name: 'John Myers, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage2,
  //   },
  //   {
  //     name: 'Maggie Shuler, MD, PHD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage3,
  //   },
  //   {
  //     name: 'Ryan Tarantola, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage4,
  //   },
  //   {
  //     name: 'Anne Barañano, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage5,
  //   },
  //   {
  //     name: 'George Peters III, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage6,
  //   },
  //   {
  //     name: 'Ahmet Ozkok, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage7,
  //   },
  //   {
  //     name: 'Kristine K. Pierce, MD',
  //     sub: 'Board-Certified Retina Specialist',
  //     image: rsiImage8,
  //   },
  // ];

  // const seg = [
  //   {
  //     name: 'Salmedina Alic, OD',
  //     sub: 'Optometrist',
  //     image: segImage1,
  //   },
  //   {
  //     name: 'Donna Corder, MD',
  //     sub: 'Ophthalmologist',
  //     image: segImage2,
  //   },
  //   {
  //     name: 'Ayesha Kidd, OD',
  //     sub: 'Optometrist',
  //     image: segImage3,
  //   },
  //   {
  //     name: 'Kelvin "Paul" McDaniel, DO',
  //     sub: 'Ophthalmologist',
  //     image: segImage4,
  //   },
  //   {
  //     name: 'Courtney Ray, MD',
  //     sub: 'Ophthalmologist',
  //     image: segImage6,
  //   },
  //   {
  //     name: 'Dan Wilder, OD',
  //     sub: 'Optometrist',
  //     image: segImage10,
  //   },
  //   {
  //     name: 'Peter Zloty, MD',
  //     sub: 'Ophthalmologist',
  //     image: segImage11,
  //   },
  //   {
  //     name: 'Murray Glusman, O.D.',
  //     sub: 'Optometrist',
  //     image: segImage12,
  //   },
  //   {
  //     name: 'Danielle Isen, D.O.',
  //     sub: 'Neuro-Ophthalmologist',
  //     image: segImage13,
  //   },
  //   {
  //     name: 'Leonard Rich, M.D',
  //     sub: 'Ophthalmologist',
  //     image: segImage14,
  //   },
  //   {
  //     name: 'Linda Slade, O.D.',
  //     sub: 'Optometrist',
  //     image: segImage15,
  //   },
  //   {
  //     name: 'Jeffrey St. John, M.D',
  //     sub: 'Ophthalmologist',
  //     image: segImage16,
  //   },
  //   {
  //     name: 'Joe Sugg Jr., M.D.',
  //     sub: 'Pediatric Ophthalmologist',
  //     image: segImage17,
  //   },
  //   {
  //     name: 'Yaffa Weaver, M.D.',
  //     sub: 'Ophthalmologist',
  //     image: segImage18,
  //   },
  //   {
  //     name: 'Mary Kathryn Wilson, O.D',
  //     sub: 'Optometrist',
  //     image: segImage19,
  //   },
  //   {
  //     name: 'Joseph Klabo, O.D.',
  //     sub: 'Optometrist',
  //     image: segImage20,
  //   },
  // ];

  const [rsi, setRsi] = useState([]);
  const [seg, setSeg] = useState([]);
  
  useEffect(() => {
    (async () => {
      try {
        const response = await axios.get(config[config.STAGING].INTRO_PHYSICIANS);
        setRsi(response.data.rsi || []);  
        setSeg(response.data.seg || []);
      } catch (error) {
        console.error("Error fetching RSI data:", error);
      }
    })();
  }, []);

  return (
    <View style={{flex: 1, justifyContent: 'space-around'}}>
      <View
        style={{
          marginTop: 10,
          backgroundColor: theme.colors.white,
          ...theme.shadows.sh1,
          flex: 0.8,
          justifyContent: 'space-around',
          borderTopLeftRadius: 10,
          borderBottomLeftRadius: 10,
          marginLeft: 12,
          padding: 10,
        }}>
        <Image source={rsi_pract} style={{width: 80, height: 50}} />
        <FlatList2
          alwaysBounceHorizontal={true}
          automaticallyAdjustContentInsets={true}
          data={rsi}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          keyExtractor={item => item.name}
          renderItem={({item}) => <ListItem item={item} />}
          // showsHorizontalScrollIndicator={true}
          bounces={false}
        />
        {/* <FlatList
            data={rsi}
            style={{display: 'flex', flexDirection: 'row', width: '100%',overflow:"scroll"}}
            showsVerticalScrollIndicator={true}
            horizontal={true}
            scrollEnabled={true}
            keyExtractor={item => item.name}
            renderItem={({item}) => <ListItem item={item} />}
          /> */}
      </View>
      <View style={{height: 20}} />
      <View
        style={{
          marginBottom: 10,
          backgroundColor: theme.colors.white,
          ...theme.shadows.sh1,
          flex: 0.8,
          justifyContent: 'space-around',
          borderTopLeftRadius: 10,
          borderBottomLeftRadius: 10,
          marginLeft: 12,
          padding: 10,
        }}>
        <Image source={sen_pract} style={{width: 100, height: 50}} />
        <FlatList2
          data={seg}
          horizontal={true}
          keyExtractor={item => item.name}
          renderItem={({item}) => <ListItem item={item} />}
          showsHorizontalScrollIndicator={false}
          bounces={false}
        />
      </View>
    </View>
  );
};

const ListItem = ({item}) => {
  return (
    <View
      style={{
        marginVertical: 2,
        marginHorizontal: 5,
        padding: 5,
        backgroundColor: theme.colors.white,
        ...theme.shadows.sh1,
        alignItems: 'center',
        justifyContent: 'center',
        width: 200,
        borderRadius: 8,
        minHeight: Dimensions.get('window').height * 0.15,
      }}>
      <View style={{flex: 1, justifyContent: 'space-evenly', alignItems: 'center'}}>
        <Image
          source={{ uri: `data:image/png;base64,${item.image}` }}
          style={{
            width: Dimensions.get('window').height / 12,
            height: Dimensions.get('window').height / 12,
            borderRadius: Dimensions.get('window').height / 24,
          }}
        />
        <View style={{
          alignItems: 'center', 
          marginTop: 8,
          paddingHorizontal: 5,
        }}>
          <CustomText
            style={{color: theme.colors.black, fontSize: Math.min(14, Dimensions.get('window').height * 0.018)}}
            numberOfLines={1}
            ellipsizeMode="tail">
            {item.name}
          </CustomText>
          <CustomText
            style={{
              color: theme.colors.gray, 
              textAlign: 'center',
              fontSize: Math.min(12, Dimensions.get('window').height * 0.016),
              marginTop: 2,
            }}
            numberOfLines={2}
            ellipsizeMode="tail">
            {item.sub}
          </CustomText>
        </View>
      </View>
    </View>
  );
};
export default Screen3;
