const fs = require('fs');
const path = require('path');

// Path to the file
const filePath = path.join(__dirname, 'screens/appointmentScreen/newAppointment/newAppointment.jsx');
// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Count original console statements
const originalLogCount = (content.match(/console\.log\(/g) || []).length;
const originalErrorCount = (content.match(/console\.error\(/g) || []).length;
const originalWarnCount = (content.match(/console\.warn\(/g) || []).length;

// Remove console.log statements
// This regex matches console.log statements that span multiple lines
content = content.replace(/console\.log\([^;]*\);/g, '');

// Remove console.error statements
content = content.replace(/console\.error\([^;]*\);/g, '');

// Remove console.warn statements
content = content.replace(/console\.warn\([^;]*\);/g, '');

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');

// Count remaining console statements
const remainingLogCount = (content.match(/console\.log\(/g) || []).length;
const remainingErrorCount = (content.match(/console\.error\(/g) || []).length;
const remainingWarnCount = (content.match(/console\.warn\(/g) || []).length;

console.log(`Removed ${originalLogCount - remainingLogCount} console.log statements`);
console.log(`Removed ${originalErrorCount - remainingErrorCount} console.error statements`);
console.log(`Removed ${originalWarnCount - remainingWarnCount} console.warn statements`);
console.log(`Remaining: ${remainingLogCount} logs, ${remainingErrorCount} errors, ${remainingWarnCount} warns`);
